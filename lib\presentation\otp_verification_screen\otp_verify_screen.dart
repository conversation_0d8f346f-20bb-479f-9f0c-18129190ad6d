import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tareek/presentation/new_password_screen/new_password_screen.dart';

import '../../services/api_service.dart';
import '../login_screen/login_screen.dart';

class OtpVerificationScreen extends StatefulWidget {
  final String? comingFrom;
  final String? userEmail;

  const OtpVerificationScreen({super.key, this.comingFrom, this.userEmail});

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
    String? _email;
  // Controllers for OTP input fields
  final List<TextEditingController> _otpControllers = List.generate(
    5,
    (index) => TextEditingController(),
  );

  // Focus nodes for OTP input fields
  final List<FocusNode> _focusNodes = List.generate(
    5,
    (index) => FocusNode(),
  );

  @override
  void initState() {
    super.initState();
    _loadEmail();
  }

  @override
  void dispose() {
    // Clean up controllers and focus nodes
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  Future<void> _loadEmail() async {
    final prefs = await SharedPreferences.getInstance();

    // If email is passed, use and store it
    if (widget.userEmail != null) {
      _email = widget.userEmail!;
      await prefs.setString('pendingEmail', _email!);
    } else {
      // Try to load from SharedPreferences
      final savedEmail = prefs.getString('pendingEmail');
      if (savedEmail != null) {
        _email = savedEmail;
      } else {
        // Handle the error gracefully (e.g., redirect back)
        print('No email found. Redirecting...');
        Navigator.pop(context);
      }
    }

    setState(() {}); // Refresh UI
  }

  @override
  Widget build(BuildContext context) {
    final comingFrom = widget.comingFrom;
     if (_email == null) {
      return const Center(child: CircularProgressIndicator());
    }
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Color(0xFF1F41BB)),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 50),
            // OTP Verification
            Container(
              alignment: Alignment.center,
              child: const Text(
                'OTP Verification',
                style: TextStyle(
                  fontSize: 30,
                  fontFamily: "Satoshi",
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F41BB),
                ),
              ),
            ),
            const SizedBox(height: 10),

            Container(
              alignment: Alignment.center,
              child: Text(
                comingFrom == 'signup'
                    ? "Enter the verification code we just sent to your email address to complete your account registration."
                    : "Enter the verification code we just sent to your email address to complete your password changing.",
                style: const TextStyle(
                  fontSize: 16,
                  fontFamily: "Satoshi",
                  color: Colors.grey,
                ),
              ),
            ),

            const SizedBox(height: 30),
            // OTP Input Fields
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(5, (index) {
                return SizedBox(
                  width: 50,
                  child: TextField(
                    controller: _otpControllers[index],
                    focusNode: _focusNodes[index],
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                    maxLength: 1,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    decoration: InputDecoration(
                      filled: true,
                      fillColor: const Color(0xFFF1F4FF),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide.none,
                      ),
                      counterText: '', // Hide character counter
                    ),
                    onChanged: (value) {
                      if (value.isNotEmpty && index < 3) {
                        // Move to next field
                        _focusNodes[index + 1].requestFocus();
                      } else if (value.isEmpty && index > 0) {
                        // Move to previous field when backspacing
                        _focusNodes[index - 1].requestFocus();
                      }
                    },
                  ),
                );
              }),
            ),
            const SizedBox(height: 30),
            // Verify Button

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  _handleVerification();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1F41BB),
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                ),
                child: const Text(
                  'Verify',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontFamily: "Satoshi",
                  ),
                ),
              ),
            ),
            const SizedBox(height: 50),
            // Resend OTP Text
            Center(
              child: GestureDetector(
                onTap: () {
                  _handleResendOTP();
                },
                child: const Text.rich(
                  TextSpan(
                    text: 'Didn\'t receive OTP code? ',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                      fontFamily: "Satoshi",
                      fontWeight: FontWeight.bold,
                    ),
                    children: [
                      TextSpan(
                        text: 'Resend',
                        style: TextStyle(
                          color: Color(0xFF1F41BB),
                          fontFamily: "Satoshi",
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleVerification() async {
    // Get the complete OTP
    String otp = _otpControllers.map((controller) => controller.text).join();

    if (otp.length != 5) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter complete OTP'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    final prefs = await SharedPreferences.getInstance();

    if (widget.comingFrom == 'signup') {
      final response = await ApiService.verifyOtp(
        email: _email!,
        otp: otp,
      );
      if (response.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(response.data['message'] ?? 'OTP verified successfully'),
            backgroundColor: Color(0xFF1F41BB),
          ),
        );
        Future.delayed(const Duration(seconds: 2), () {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const LoginScreen(),
            ),
          );
        });
        await prefs.remove('pendingEmail');
      } else {
        print('Error verifying OTP: ${response.statusCode}');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.data['message'] ?? 'Error verifying OTP'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else if (widget.comingFrom == 'forgotPassword') {
      final response = await ApiService.forgotPassword(
        email: _email!,
      );
      if (response.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.data['message'] ?? 'OTP sent successfully'),
            backgroundColor: Color(0xFF1F41BB),
          ),
        );
        Future.delayed(const Duration(seconds: 2), () {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const NewPasswordScreen(),
            ),
          );
        });
        await prefs.remove('pendingEmail');
      } else {
        print('Error verifying OTP: ${response.statusCode}');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.data['message'] ?? 'Error sending OTP'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleResendOTP() async {
    final response = await ApiService.resendOtp(
      email: _email!,
    );
    if (response.statusCode == 200) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(response.data['message'] ??
              'OTP resent successfully, check your email.'),
          backgroundColor: Color(0xFF1F41BB),
        ),
      );
    } else {
      print('Error resending OTP: ${response.statusCode}');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(response.data['message'] ?? 'Error resending OTP'),
          backgroundColor: Colors.red,
        ),
      );
    }

    // Clear existing OTP fields
    for (var controller in _otpControllers) {
      controller.clear();
    }

    // Focus on first field
    _focusNodes[0].requestFocus();
  }
}
