import 'package:flutter/material.dart';

class CreateVacanciesScreen extends StatefulWidget {
  const CreateVacanciesScreen({Key? key}) : super(key: key);

  @override
  State<CreateVacanciesScreen> createState() => _CreateVacanciesScreenState();
}

class _CreateVacanciesScreenState extends State<CreateVacanciesScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final _formKey = GlobalKey<FormState>();

  // Step 1 Controllers
  final TextEditingController _positionController = TextEditingController();
  final TextEditingController _salaryController = TextEditingController();
  String? _selectedLocation;
  String? _selectedType;

  // Step 2 - Requirements
  final List<String> _requirements = [
    'Portfolio: A strong, diverse portfolio showcasing design skills across different stages of the product lifecycle.',
    'Education: Bachelor\'s degree in Design, HCI, or a related field (or equivalent practical experience).',
    'Experience: 3-5 years of product design experience, preferably in SaaS, web, or mobile applications.',
    'Experience',
  ];
  final TextEditingController _newRequirementController =
      TextEditingController();

  // Step 1 - Job Description (moved from step 3)
  final TextEditingController _descriptionController = TextEditingController();

  bool _showSuccessDialog = false;

  final List<String> _locations = [
    'New York',
    'San Francisco',
    'Los Angeles',
    'Chicago',
    'Remote'
  ];

  final List<String> _jobTypes = [
    'Full-time',
    'Part-time',
    'Contract',
    'Internship'
  ];

  bool _isStep1Valid() {
    return _positionController.text.isNotEmpty &&
        _salaryController.text.isNotEmpty &&
        _selectedLocation != null &&
        _selectedType != null &&
        _descriptionController.text.isNotEmpty;
  }

  bool _isStep2Valid() {
    return _requirements.isNotEmpty;
  }

  void _nextStep() {
    if (_currentStep < 2) {
      bool canProceed = false;

      switch (_currentStep) {
        case 0:
          canProceed = _isStep1Valid();
          if (!canProceed) {
            _showValidationError('Please fill in all required fields');
            return;
          }
          break;
        case 1:
          canProceed = _isStep2Valid();
          if (!canProceed) {
            _showValidationError('Please add at least one requirement');
            return;
          }
          break;
      }

      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showValidationError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _addRequirement() {
    if (_newRequirementController.text.trim().isNotEmpty) {
      setState(() {
        _requirements.add(_newRequirementController.text.trim());
        _newRequirementController.clear();
      });
    }
  }

  void _removeRequirement(int index) {
    setState(() {
      _requirements.removeAt(index);
    });
  }

  void _showSuccessMessage() {
    setState(() {
      _currentStep = 2;
    });
    _pageController.animateToPage(
      2,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Stack(
        children: [
          SafeArea(
            child: Column(
              children: [
                // Header with Stepper
                Container(
                  color: Colors.white,
                  child: Column(
                    children: [
                      // App Bar
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom:
                                BorderSide(color: Color(0xFFE0E0E0), width: 1),
                          ),
                        ),
                        child: Row(
                          children: [
                            IconButton(
                              onPressed: () => Navigator.pop(context),
                              icon: const Icon(Icons.arrow_back_ios, size: 20),
                            ),
                            const Expanded(
                              child: Text(
                                'Create Vacancies',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            IconButton(
                              onPressed: () {},
                              icon: const Icon(Icons.more_vert),
                            ),
                          ],
                        ),
                      ),

                      // Step Indicator
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 16),
                        child: Row(
                          children: [
                            _buildStepIndicator(0, 'Details'),
                            _buildStepConnector(0),
                            _buildStepIndicator(1, 'Requirements'),
                            _buildStepConnector(1),
                            _buildStepIndicator(2, 'Success'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Content
                Expanded(
                  child: PageView(
                    controller: _pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      _buildStep1(),
                      _buildStep2(),
                      _buildStep3(), // Success screen
                    ],
                  ),
                ),

                // Bottom Navigation
                if (_currentStep < 2)
                  Container(
                    padding: const EdgeInsets.all(20),
                    color: Colors.white,
                    child: Row(
                      children: [
                        if (_currentStep > 0)
                          Expanded(
                            child: OutlinedButton(
                              onPressed: _previousStep,
                              style: OutlinedButton.styleFrom(
                                side:
                                    const BorderSide(color: Color(0xFFE0E0E0)),
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: const Text(
                                'Previous',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF424242),
                                ),
                              ),
                            ),
                          ),
                        if (_currentStep > 0) const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _currentStep < 1
                                ? _nextStep
                                : _showSuccessMessage,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF4285F4),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              _currentStep < 1 ? 'Next' : 'Post Job',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),

          // Success Dialog Overlay
          // Removed - success is now Step 3
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int step, String label) {
    final isActive = step <= _currentStep;
    final isCurrent = step == _currentStep;

    return Expanded(
      child: Column(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color:
                  isActive ? const Color(0xFF4285F4) : const Color(0xFFE0E0E0),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: isActive && step < _currentStep
                  ? const Icon(Icons.check, color: Colors.white, size: 18)
                  : Text(
                      '${step + 1}',
                      style: TextStyle(
                        color:
                            isActive ? Colors.white : const Color(0xFF9E9E9E),
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color:
                  isCurrent ? const Color(0xFF4285F4) : const Color(0xFF9E9E9E),
              fontWeight: isCurrent ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepConnector(int step) {
    final isCompleted = step < _currentStep;

    return Expanded(
      child: Container(
        height: 2,
        margin: const EdgeInsets.only(bottom: 24),
        color: isCompleted ? const Color(0xFF4285F4) : const Color(0xFFE0E0E0),
      ),
    );
  }

  Widget _buildStep1() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Upload Company Logo
            Center(
              child: Column(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF0F0F0),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: const Color(0xFFE0E0E0),
                        style: BorderStyle.solid,
                      ),
                    ),
                    child: const Icon(
                      Icons.home_outlined,
                      size: 32,
                      color: Color(0xFF9E9E9E),
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'Upload Company Logo',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF757575),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Open Position
            _buildTextField(
              label: 'Open Position*',
              controller: _positionController,
              hintText: 'Enter position title',
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Position is required';
                }
                return null;
              },
            ),

            const SizedBox(height: 20),

            // Salary
            _buildTextField(
              label: 'Salary*',
              controller: _salaryController,
              hintText: 'Enter salary range',
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Salary is required';
                }
                return null;
              },
            ),

            const SizedBox(height: 20),

            // Location Dropdown
            _buildDropdown(
              label: 'Location*',
              value: _selectedLocation,
              items: _locations,
              onChanged: (value) {
                setState(() {
                  _selectedLocation = value;
                });
              },
            ),

            const SizedBox(height: 20),

            // Type Dropdown
            _buildDropdown(
              label: 'Type*',
              value: _selectedType,
              items: _jobTypes,
              onChanged: (value) {
                setState(() {
                  _selectedType = value;
                });
              },
            ),

            const SizedBox(height: 20),

            // Job Description
            _buildTextField(
              label: 'Job Description*',
              controller: _descriptionController,
              maxLines: 6,
              hintText:
                  'Enter detailed job description including responsibilities, qualifications, and benefits...',
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Job description is required';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStep2() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Job Requirements',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Color(0xFF212121),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Add requirements for this position',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF757575),
            ),
          ),

          const SizedBox(height: 24),

          // Requirements List
          ..._requirements.asMap().entries.map((entry) {
            int index = entry.key;
            String requirement = entry.value;

            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 2, right: 12),
                    width: 20,
                    height: 20,
                    decoration: const BoxDecoration(
                      color: Color(0xFF4285F4),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 14,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      requirement,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF424242),
                        height: 1.4,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => _removeRequirement(index),
                    icon: const Icon(
                      Icons.close,
                      size: 18,
                      color: Color(0xFF9E9E9E),
                    ),
                    constraints: const BoxConstraints(),
                    padding: const EdgeInsets.all(4),
                  ),
                ],
              ),
            );
          }).toList(),

          const SizedBox(height: 16),

          // Add New Requirement
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _newRequirementController,
                  decoration: InputDecoration(
                    hintText: 'Add a new requirement...',
                    hintStyle: const TextStyle(
                      color: Color(0xFF9E9E9E),
                      fontSize: 14,
                    ),
                    filled: true,
                    fillColor: const Color(0xFFF8F9FA),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFF4285F4)),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  onFieldSubmitted: (_) => _addRequirement(),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: _addRequirement,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4285F4),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  minimumSize: const Size(48, 48),
                ),
                child: const Icon(Icons.add, size: 20),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStep3() {
    return Container(
      color: const Color(0xFFF5F5F5),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(40),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Success Icon
              Container(
                width: 120,
                height: 120,
                decoration: const BoxDecoration(
                  color: Color(0xFF4CAF50),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 60,
                ),
              ),

              const SizedBox(height: 40),

              // Success Title
              const Text(
                'Job Application sent successfully',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF212121),
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 16),

              // Success Subtitle
              const Text(
                'Your Job Application have\nsuccessfully Uploaded',
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF757575),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 60),

              // Action Buttons
              Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF4285F4),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 18),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Go to home',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: () {
                        _resetForm();
                      },
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Color(0xFFE0E0E0)),
                        backgroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 18),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Post another job',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF424242),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    String? hintText,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF424242),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: const TextStyle(
              color: Color(0xFF9E9E9E),
              fontSize: 14,
            ),
            filled: true,
            fillColor: const Color(0xFFF8F9FA),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF4285F4)),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdown({
    required String label,
    required String? value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF424242),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F9FA),
            border: Border.all(color: const Color(0xFFE0E0E0)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              hint: const Text(
                'Select an option',
                style: TextStyle(
                  color: Color(0xFF9E9E9E),
                  fontSize: 14,
                ),
              ),
              items: items.map((String item) {
                return DropdownMenuItem<String>(
                  value: item,
                  child: Text(
                    item,
                    style: const TextStyle(fontSize: 14),
                  ),
                );
              }).toList(),
              onChanged: onChanged,
              isExpanded: true,
              icon: const Icon(
                Icons.keyboard_arrow_down,
                color: Color(0xFF9E9E9E),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _resetForm() {
    _positionController.clear();
    _salaryController.clear();
    _descriptionController.clear();
    _newRequirementController.clear();
    setState(() {
      _selectedLocation = null;
      _selectedType = null;
      _currentStep = 0;
      _requirements.clear();
      _requirements.addAll([
        'Portfolio: A strong, diverse portfolio showcasing design skills across different stages of the product lifecycle.',
        'Education: Bachelor\'s degree in Design, HCI, or a related field (or equivalent practical experience).',
        'Experience: 3-5 years of product design experience, preferably in SaaS, web, or mobile applications.',
        'Experience',
      ]);
    });
    _pageController.animateToPage(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _positionController.dispose();
    _salaryController.dispose();
    _descriptionController.dispose();
    _newRequirementController.dispose();
    super.dispose();
  }
}
