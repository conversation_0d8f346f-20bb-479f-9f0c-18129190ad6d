import 'package:flutter/material.dart';

class BillingScreen extends StatefulWidget {
  @override
  _BillingScreenState createState() => _BillingScreenState();
}

class _BillingScreenState extends State<BillingScreen> {
  int selectedPlan = 1; // 0: Basic, 1: Medium, 2: Advanced

  List<Map<String, dynamic>> plans = [
    {
      'name': 'Tarif Basic',
      'price': '\$9.99USD/m',
      'description': 'Basic plans with Ads',
    },
    {
      'name': 'Tarif Medium',
      'price': '\$30.99USD/m',
      'description': 'Medium plans with Ads',
    },
    {
      'name': 'Tarif Advanced',
      'price': '\$54.99USD/m',
      'description': 'Basic plans with Ads',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Billing',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          children: [
            ...plans.asMap().entries.map((entry) {
              int index = entry.key;
              Map<String, dynamic> plan = entry.value;
              return _buildPlanCard(plan, index == selectedPlan, () {
                setState(() {
                  selectedPlan = index;
                });
              });
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanCard(
      Map<String, dynamic> plan, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(bottom: 16),
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected ? Color(0xFF4A90E2) : Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 6,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  plan['name'],
                  style: TextStyle(
                    fontSize: 14,
                    color: isSelected ? Colors.white : Colors.grey[600],
                  ),
                ),
                Text(
                  plan['price'],
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isSelected ? Colors.white : Colors.black87,
                  ),
                ),
                Text(
                  plan['description'],
                  style: TextStyle(
                    fontSize: 12,
                    color: isSelected ? Colors.white70 : Colors.grey[500],
                  ),
                ),
              ],
            ),
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? Colors.white : Colors.grey[400]!,
                  width: 2,
                ),
                color: isSelected ? Colors.white : Colors.transparent,
              ),
              child: isSelected
                  ? Icon(Icons.check, size: 16, color: Color(0xFF4A90E2))
                  : null,
            ),
          ],
        ),
      ),
    );
  }
}
