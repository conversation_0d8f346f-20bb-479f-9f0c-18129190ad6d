import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:tareek/presentation/login_screen/login_screen.dart';
import 'package:tareek/presentation/otp_verification_screen/otp_verify_screen.dart';
import 'package:tareek/widgets/bottom_navigation.dart';

import '../../services/api_service.dart';

class SignupScreen extends StatefulWidget {
  final String? selectedOption;

  const SignupScreen({super.key, this.selectedOption});

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {
  // Text editing controllers for form fields
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  String? _selectedUserType;

  // Form key for validation
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _selectedUserType = widget.selectedOption;
  }

  // Password visibility toggles
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;

  // Google Sign-In
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  @override
  void dispose() {
    // Clean up controllers when the widget is disposed
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Color(0xFF1F41BB)),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 60),

                // Login Header
                const Text(
                  'Create Account',
                  style: TextStyle(
                    fontSize: 30,
                    fontFamily: "Satoshi",
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1F41BB),
                  ),
                ),
                const SizedBox(height: 10),

                Text(
                  "Create an account  so you can explore all the existing jobs",
                  style: const TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                    fontFamily: "Satoshi",
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 35),

                // Name Field
                TextFormField(
                  controller: _nameController,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your name';
                    }
                    return null;
                  },
                  decoration: InputDecoration(
                    labelText: 'Name',
                    filled: true,
                    fillColor: const Color(0xFFF1F4FF),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                      borderSide: const BorderSide(
                        color: Color(0xFF1F41BB),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Email Field
                TextFormField(
                  controller: _emailController,
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your email';
                    }
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                        .hasMatch(value)) {
                      return 'Please enter a valid email';
                    }
                    return null;
                  },
                  decoration: InputDecoration(
                    labelText: 'Email',
                    filled: true,
                    fillColor: const Color(0xFFF1F4FF),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                      borderSide: const BorderSide(
                        color: Color(0xFF1F41BB),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Password Field
                TextFormField(
                  controller: _passwordController,
                  obscureText: !_isPasswordVisible,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your password';
                    }
                    if (value.length < 6) {
                      return 'Password must be at least 6 characters';
                    }
                    return null;
                  },
                  decoration: InputDecoration(
                    labelText: 'Password',
                    filled: true,
                    fillColor: const Color(0xFFF1F4FF),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _isPasswordVisible
                            ? Icons.visibility
                            : Icons.visibility_off,
                        color: const Color(0xFF1F41BB),
                      ),
                      onPressed: () {
                        setState(() {
                          _isPasswordVisible = !_isPasswordVisible;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                      borderSide: const BorderSide(
                        color: Color(0xFF1F41BB),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Confirm Password Field
                TextFormField(
                  controller: _confirmPasswordController,
                  obscureText: !_isConfirmPasswordVisible,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please confirm your password';
                    }
                    if (value != _passwordController.text) {
                      return 'Passwords do not match';
                    }
                    return null;
                  },
                  decoration: InputDecoration(
                    labelText: 'Confirm Password',
                    filled: true,
                    fillColor: const Color(0xFFF1F4FF),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _isConfirmPasswordVisible
                            ? Icons.visibility
                            : Icons.visibility_off,
                        color: const Color(0xFF1F41BB),
                      ),
                      onPressed: () {
                        setState(() {
                          _isConfirmPasswordVisible =
                              !_isConfirmPasswordVisible;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                      borderSide: const BorderSide(
                        color: Color(0xFF1F41BB),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                if (_selectedUserType == null || _selectedUserType == "")
                  DropdownButtonFormField<String>(
                    value: _selectedUserType,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select your user type';
                      }
                      return null;
                    },
                    onChanged: (String? newValue) {
                      setState(() {
                        _selectedUserType = newValue;
                      });
                    },
                    items: const [
                      DropdownMenuItem<String>(
                        value: 'job_seeker',
                        child: Text('Job Seeker'),
                      ),
                      DropdownMenuItem<String>(
                        value: 'recruiter',
                        child: Text('Recruiter'),
                      ),
                    ],
                    decoration: InputDecoration(
                      labelText: 'User Type',
                      filled: true,
                      fillColor: const Color(0xFFF1F4FF),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: const BorderSide(
                          color: Color(0xFF1F41BB),
                        ),
                      ),
                    ),
                  ),

                const SizedBox(height: 50),

                // Sign Up Button
                ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      // Handle signup logic here
                      _handleSignup();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF1F41BB),
                    minimumSize: const Size(double.infinity, 50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                  ),
                  child: const Text(
                    'Sign up',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontFamily: "Satoshi",
                    ),
                  ),
                ),
                const SizedBox(height: 10),

                // Already Have Account Button
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const LoginScreen(),
                      ),
                    );
                  },
                  child: const Text(
                    'Already Have an account',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                      fontFamily: "Satoshi",
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 80),

                // Divider with "or signup with"
                const Row(
                  children: [
                    Expanded(
                      child: Divider(
                        color: Colors.grey,
                        thickness: 1,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10.0),
                      child: Text(
                        'or signup with',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ),
                    Expanded(
                      child: Divider(
                        color: Colors.grey,
                        thickness: 1,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Social Media Buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Facebook Button
                    _buildSocialButton(
                      'assets/facebook.svg',
                      () => _handleSocialSignup('Facebook'),
                    ),
                    // Google Button
                    _buildSocialButton(
                      'assets/google.svg',
                      () => _handleSocialSignup('Google'),
                    ),
                    // Apple Button
                    _buildSocialButton(
                      'assets/apple.svg',
                      () => _handleSocialSignup('Apple'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build social media buttons
  Widget _buildSocialButton(String assetPath, VoidCallback onPressed) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: const BorderSide(color: Colors.grey, width: 0.5),
        ),
        padding: const EdgeInsets.all(12),
        minimumSize: const Size(60, 48),
      ),
      child: SvgPicture.asset(
        assetPath,
        height: 24,
        width: 24,
      ),
    );
  }

  // Handle regular signup
  Future<void> _handleSignup() async {
    // Show loading or process signup
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Creating account for ...'),
        backgroundColor: const Color(0xFF1F41BB),
      ),
    );

    final response = await ApiService.register(
      name: _nameController.text,
      email: _emailController.text,
      password: _passwordController.text,
      userType: _selectedUserType!,
    );
    if (response.statusCode == 201) {
      print('Registration successful: ${response.data}');
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(response.data['message'] ??
              'Account created successfully!, Please check your email for verification code'),
          backgroundColor: const Color(0xFF1F41BB),
        ),
      );
      Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => OtpVerificationScreen(
              comingFrom: 'signup',
              userEmail: _emailController.text,
            ),
          ),
        );
      });
    } else if (response.statusCode == 400) {
      print('Validation failed: ${response.data}');
      // Show validation error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(response.data['error'] ?? 'Validation failed'),
          backgroundColor: Colors.red,
        ),
      );
       Future.delayed(const Duration(seconds: 2), () {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => OtpVerificationScreen(
              comingFrom: 'signup',
              userEmail: _emailController.text,
            ),
          ),
        );
      });
    } else {
      print('Registration failed: ${response.data}');
      // Show generic error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(response.data['message'] ?? 'Registration failed'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Handle social media signup
  Future<void> _handleSocialSignup(String provider) async {
    if (provider == 'Google') {
      await _handleGoogleSignIn();
    } else {
      // Handle other providers (Facebook, Apple)
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Signing up with $provider - Coming Soon!'),
          backgroundColor: const Color(0xFF1F41BB),
        ),
      );
    }
  }

// Handle Google Sign-In
  Future<void> _handleGoogleSignIn() async {
    try {
      Response response;

      // Show loading
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Signing in with Google...'),
          backgroundColor: Color(0xFF1F41BB),
        ),
      );

      // Sign out any existing user first
      await _googleSignIn.signOut();

      // Attempt to sign in
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // User cancelled the sign-in
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Google sign-in cancelled'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Get authentication details
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      if (googleAuth.idToken == null) {
        throw Exception('Failed to get ID token from Google');
      }

      // Call your API service with the ID token
      if (widget.selectedOption == null || widget.selectedOption == "") {
        response = await ApiService.googleSignIn(
          idToken: googleAuth.idToken!,
        );
      } else {
        response = await ApiService.googleSignIn(
          idToken: googleAuth.idToken!,
          userType: widget.selectedOption!,
        );
      }

      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('Google Sign-In successful: ${response.data}');

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(response.data['message'] ?? 'Google sign-in successful!'),
            backgroundColor: const Color(0xFF1F41BB),
          ),
        );

        // Navigate to appropriate screen based on your app flow
        // For example, if the user needs to verify email or complete profile:
        if (response.data['needsVerification'] == true) {
          Future.delayed(const Duration(seconds: 1), () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (context) => OtpVerificationScreen(
                  comingFrom: 'google_signup',
                  userEmail: googleUser.email,
                ),
              ),
            );
          });
        } else {
          // Navigate to main app or dashboard
          Future.delayed(const Duration(seconds: 1), () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (context) => const BottomNavBar(),
              ),
            );
          });
        }
      } else {
        print('Google Sign-In failed: ${response.data}');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.data['message'] ??
                'Google sign-in failed. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (error) {
      print('Google Sign-In error: $error');
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Google sign-in failed: ${error.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
