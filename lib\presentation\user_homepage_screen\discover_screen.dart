import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../services/api_service.dart';
import 'package:tareek/presentation/user_homepage_screen/event_details_screen.dart';
import 'news_details_screen.dart';

class DiscoverScreen extends StatefulWidget {
  @override
  _DiscoverScreenState createState() => _DiscoverScreenState();
}

class _DiscoverScreenState extends State<DiscoverScreen> {
  bool isFavorite = false;
  bool isLoading = false;
  bool _isLiked = false;
  int _eventLikesCount = 0;
  bool _isLiking = false;
  bool _isFaving = false;
  bool _isFav = false;
  int selectedButton = 0;
  late Map<dynamic, dynamic> _user = {};
  String? _userId;
  List<dynamic> _events = [];
  List<dynamic> _news = [];
  List<dynamic> _jobs = [];
  List<dynamic> _jobTypes = [];
  bool _isLoadingNews = true;
  bool _isLoadingEvents = true;
  bool _isLoadingJobs = true;
  bool _isLoadingJobTypes = true;
  List<dynamic> _newsCategories = [];
  List<dynamic> _eventCategories = [];
  bool _isLoadingNewsCategories = true;
  bool _isLoadingEventCategories = true;
  String eventId = '';
  String newsId = '';

  int _currentPage = 1;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  // Search and filter variables
  TextEditingController _searchController = TextEditingController();
  String _selectedCategoryId = '';
  String _selectedJobType = '';
  String _searchQuery = '';

  // Multiple selection filter variables
  Set<String> _selectedCategoryIds = {};
  Set<String> _selectedJobTypes = {};

  ScrollController _scrollController = ScrollController();

  Map<String, bool> _eventLikeStates = {};
  Map<String, bool> _eventFavoriteStates = {};
  Map<String, bool> _newsFavoriteStates = {};

  // Function to handle button selection
  void onButtonPressed(int index) {
    setState(() {
      selectedButton = index;
      _currentPage = 1;
      _hasMoreData = true;
      _searchQuery = '';
      _selectedCategoryId = '';
      _selectedJobType = '';
      _selectedCategoryIds.clear();
      _selectedJobTypes.clear();
      _searchController.clear();
    });
    switch (index) {
      case 0:
        _jobs.clear();
        getJobs(isRefresh: true);
        break;
      case 1:
        _news.clear();
        getNews(isRefresh: true);
        break;
      case 2:
        _events.clear();
        getEvents(isRefresh: true);
        break;
    }
  }

  void initState() {
    super.initState();
    _initializeData();
    _scrollController.addListener(_scrollListener);
  }

  Future<void> _initializeData() async {
    await _loadUserData();

    await Future.wait([
      getNews(),
      getNewsCategories(),
      getEventCategories(),
      getJobTypes(),
      getEvents(),
    ]);

    // Note: We no longer call _checkIfUserLikedEvent here since eventId is empty
    // Individual event like states will be checked when building event cards
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      if (_hasMoreData && !_isLoadingMore) {
        _loadMoreData();
      }
    }
  }

  void _loadMoreData() {
    if (!_hasMoreData || _isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    switch (selectedButton) {
      case 0:
        getJobs();
        break;
      case 1:
        getNews();
        break;
      case 2:
        getEvents();
        break;
    }
  }

  void _performSearch() {
    setState(() {
      _currentPage = 1;
      _hasMoreData = true;
      _searchQuery = _searchController.text;
    });

    switch (selectedButton) {
      case 0:
        _jobs.clear();
        getJobs(isRefresh: true);
        break;
      case 1:
        _news.clear();
        getNews(isRefresh: true);
        break;
      case 2:
        _events.clear();
        getEvents(isRefresh: true);
        break;
    }
  }

  // New methods for multiple selection filtering
  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FilterBottomSheet(
        selectedButton: selectedButton,
        jobTypes: _jobTypes,
        newsCategories: _newsCategories,
        eventCategories: _eventCategories,
        selectedJobTypes: _selectedJobTypes,
        selectedCategoryIds: _selectedCategoryIds,
        isLoadingJobTypes: _isLoadingJobTypes,
        isLoadingNewsCategories: _isLoadingNewsCategories,
        isLoadingEventCategories: _isLoadingEventCategories,
        onApplyFilters: (Set<String> jobTypes, Set<String> categoryIds) {
          setState(() {
            _selectedJobTypes = jobTypes;
            _selectedCategoryIds = categoryIds;
            _currentPage = 1;
            _hasMoreData = true;
          });
          _applyFiltersAfterSelection();
          Navigator.pop(context);
        },
        onClearFilters: () {
          setState(() {
            _selectedCategoryIds.clear();
            _selectedJobTypes.clear();
            _selectedCategoryId = '';
            _selectedJobType = '';
            _currentPage = 1;
            _hasMoreData = true;
          });
          _applyFiltersAfterSelection();
          Navigator.pop(context);
        },
      ),
    );
  }

  void _applyFiltersAfterSelection() {
    switch (selectedButton) {
      case 0:
        _jobs.clear();
        getJobs(isRefresh: true);
        break;
      case 1:
        _news.clear();
        getNews(isRefresh: true);
        break;
      case 2:
        _events.clear();
        getEvents(isRefresh: true);
        break;
    }
  }

  String formatEventDateTime(String? date, String? time) {
    if (date == null || time == null) return 'Date/time unavailable';

    try {
      // Combine date and time into a single DateTime
      final dateTime = DateTime.parse('$date $time');

      // Format the datetime
      final formatted =
          DateFormat('EEEE MMMM d, y \'at\' HH:mm').format(dateTime);
      return formatted;
    } catch (e) {
      return 'Invalid date/time';
    }
  }

  Future<void> _loadUserData() async {
    final prefs = await SharedPreferences.getInstance();

    final userString = prefs.getString('user');
    final userId = prefs.getString('user_id');

    if (userString != null) {
      final user = jsonDecode(userString);
      setState(() {
        _user = user;
        _userId = userId;
      });
    } else {
      print('No user found. Redirecting...');
    }
  }

  Future<void> getNews({bool isRefresh = false}) async {
    if (isRefresh) {
      setState(() {
        _isLoadingNews = true;
      });
    }

    try {
      // Use multiple categories if selected, otherwise fall back to single selection
      String? categoryFilter;
      if (_selectedCategoryIds.isNotEmpty) {
        categoryFilter =
            _selectedCategoryIds.first; // For now, use first selected
      } else if (_selectedCategoryId.isNotEmpty) {
        categoryFilter = _selectedCategoryId;
      }

      final response = await ApiService.getNews(
        limit: 10,
        page: _currentPage,
        search: _searchQuery,
        category_id: categoryFilter,
      );
      if (response.statusCode == 200) {
        final responseData = response.data;
        final newData = responseData['data'] ?? [];
        final pagination = responseData['pagination'];

        setState(() {
          if (isRefresh || _currentPage == 1) {
            _news = newData;
          } else {
            _news.addAll(newData);
          }
          _hasMoreData = pagination['hasNextPage'] ?? false;
          _isLoadingNews = false;
          _isLoadingMore = false;
        });
        _checkNewsFavoriteStates();
        print('News loaded: ${_news.length}');
      } else {
        print('Error getting news: ${response.statusCode}');
        setState(() {
          _isLoadingNews = false;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      print('Error fetching news: $e');
      setState(() {
        _isLoadingNews = false;
        _isLoadingMore = false;
      });
    }
  }

  // get news categories
  Future<void> getNewsCategories() async {
    try {
      final response = await ApiService.getNewsCategories();
      print('News Categories API Response: ${response.data}');
      if (response.statusCode == 200) {
        final responseData = response.data;
        setState(() {
          _newsCategories = responseData['categories'] ?? [];
          _isLoadingNewsCategories = false;
        });
        print('News Categories loaded: ${_newsCategories.length}');
        print('News Categories data: $_newsCategories');
      } else {
        print('Error getting news categories: ${response.statusCode}');
        setState(() {
          _isLoadingNewsCategories = false;
        });
      }
    } catch (e) {
      print('Error fetching news categories: $e');
      setState(() {
        _isLoadingNewsCategories = false;
      });
    }
  }

  // get events categories
  Future<void> getEventCategories() async {
    try {
      final response = await ApiService.getEventCategories();
      print('Event Categories API Response: ${response.data}');
      if (response.statusCode == 200) {
        final responseData = response.data;
        setState(() {
          _eventCategories = responseData['categories'] ?? [];
          _isLoadingEventCategories = false;
        });
        print('Event Categories loaded: ${_eventCategories.length}');
        print('Event Categories data: $_eventCategories');
      } else {
        print('Error getting event categories: ${response.statusCode}');
        setState(() {
          _isLoadingEventCategories = false;
        });
      }
    } catch (e) {
      print('Error fetching event categories: $e');
      setState(() {
        _isLoadingEventCategories = false;
      });
    }
  }

// get job types
  Future<void> getJobTypes() async {
    try {
      final response = await ApiService.getJobTypes();
      if (response.statusCode == 200) {
        final responseData = response.data;
        setState(() {
          _jobTypes = responseData['job_types'] ?? [];
          _isLoadingJobTypes = false;
        });
        print('Job Types loaded: ${_jobTypes.length}');
      } else {
        print('Error getting job types: ${response.statusCode}');
        setState(() {
          _isLoadingJobTypes = false;
        });
      }
    } catch (e) {
      print('Error fetching job types: $e');
      setState(() {
        _isLoadingJobTypes = false;
      });
    }
  }

  Future<void> getEvents({bool isRefresh = false}) async {
    if (isRefresh) {
      setState(() {
        _isLoadingEvents = true;
      });
    }

    try {
      // Use multiple categories if selected, otherwise fall back to single selection
      String? categoryFilter;
      if (_selectedCategoryIds.isNotEmpty) {
        categoryFilter =
            _selectedCategoryIds.first; // For now, use first selected
      } else if (_selectedCategoryId.isNotEmpty) {
        categoryFilter = _selectedCategoryId;
      }

      final response = await ApiService.getEvents(
        limit: 10,
        page: _currentPage,
        search: _searchQuery,
        category_id: categoryFilter,
      );
      if (response.statusCode == 200) {
        final responseData = response.data;
        final newData = responseData['data'] ?? [];
        final pagination = responseData['pagination'];

        setState(() {
          if (isRefresh || _currentPage == 1) {
            _events = newData;
          } else {
            _events.addAll(newData);
          }
          _hasMoreData = pagination['hasNextPage'] ?? false;
          _isLoadingEvents = false;
          _isLoadingMore = false;
        });
        _checkEventLikeStates();
        _checkEventsFavoriteStates();
        print('Events loaded: ${_events.length}');
      } else {
        print('Error getting events: ${response.statusCode}');
        setState(() {
          _isLoadingEvents = false;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      print('Error fetching events: $e');
      setState(() {
        _isLoadingEvents = false;
        _isLoadingMore = false;
      });
    }
  }

  Future<void> getJobs({bool isRefresh = false}) async {
    if (_userId == null) return;

    if (isRefresh) {
      setState(() {
        _isLoadingJobs = true;
      });
    }

    try {
      // Use multiple job types if selected, otherwise fall back to single selection
      String? jobTypeFilter;
      if (_selectedJobTypes.isNotEmpty) {
        jobTypeFilter = _selectedJobTypes.first; // For now, use first selected
      } else if (_selectedJobType.isNotEmpty) {
        jobTypeFilter = _selectedJobType;
      }

      final response = await ApiService.getRecommendedJobs(
        userId: _userId!,
        page: _currentPage,
        limit: 10,
        search: _searchQuery.isEmpty ? null : _searchQuery,
        jobType: jobTypeFilter,
      );
      if (response.statusCode == 200) {
        final responseData = response.data;
        final newData = responseData['data'] ?? [];
        final pagination = responseData['pagination'];

        setState(() {
          if (isRefresh || _currentPage == 1) {
            _jobs = newData;
          } else {
            _jobs.addAll(newData);
          }
          _hasMoreData = pagination['hasNextPage'] ?? false;
          _isLoadingJobs = false;
          _isLoadingMore = false;
        });
        print('Jobs loaded: ${_jobs.length}');
      } else {
        print('Error getting jobs: ${response.statusCode}');
        setState(() {
          _isLoadingJobs = false;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      print('Error fetching jobs: $e');
      setState(() {
        _isLoadingJobs = false;
        _isLoadingMore = false;
      });
    }
  }

  Future<void> _checkEventLikeStates() async {
    if (_userId == null || _events.isEmpty) return;

    for (var event in _events) {
      final eventId = event['id']?.toString();
      if (eventId != null && !_eventLikeStates.containsKey(eventId)) {
        _checkIfUserLikedEvent(eventId);
      }
    }
  }

  Future<void> _checkEventsFavoriteStates() async {
    if (_userId == null || _events.isEmpty) return;

    for (var event in _events) {
      final eventId = event['id']?.toString();
      if (eventId != null && !_eventLikeStates.containsKey(eventId)) {
        _checkIfUserFavoritedEvents(eventId);
      }
    }
  }

  // Check favorite states for all loaded news
  Future<void> _checkNewsFavoriteStates() async {
    if (_userId == null || _news.isEmpty) return;

    for (var news in _news) {
      final newsId = news['id']?.toString();
      if (newsId != null && !_newsFavoriteStates.containsKey(newsId)) {
        _checkIfUserFavoritedNews(newsId);
      }
    }
  }
  // Check favorite states for all loaded news

  Future<void> _toggleNewsFavorite(
    String newsId,
  ) async {
    if (isLoading || _userId == null) return;
    setState(() {
      isLoading = true;
    });

    try {
      final currentState = _newsFavoriteStates[newsId] ?? false;
      if (currentState) {
        // Remove from favorites
        final response = await ApiService.removeNewsFromUserFavorites(
          newsId,
          _userId!,
        );

        if (response.statusCode == 200) {
          setState(() {
            _newsFavoriteStates[newsId] = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Removed from favourites!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to remove from favourites',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        // Add to favorites
        final response = await ApiService.addNewsToUserFavorites(
          newsId,
          _userId!,
        );

        if (response.statusCode == 200) {
          setState(() {
            _newsFavoriteStates[newsId] = true;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Added to favourites!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to add to favourites',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'An error occurred. Please try again.',
            style: TextStyle(fontFamily: "Satoshi"),
          ),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _checkIfUserLikedEvent(String eventId) async {
    print("event id $eventId");
    print("user id $_userId");
    if (_userId == null || eventId.isEmpty) return;
    try {
      final response = await ApiService.checkIfUserLikedEvent(
        eventId,
        _userId!,
      );

      if (response.statusCode == 200) {
        setState(() {
          _eventLikeStates[eventId] = response.data['is_liked'] ?? false;
        });
      }
    } catch (e) {
      print('Error checking if user liked event: $e');
    }
  }

  Future<void> _checkIfUserFavoritedNews(String newsId) async {
    if (_userId == null || newsId.isEmpty) return;

    try {
      final response =
          await ApiService.checkIfUserFavoritedNews(newsId, _userId!);
      if (response.statusCode == 200) {
        setState(() {
          _newsFavoriteStates[newsId] = response.data['is_favorited'] ?? false;
        });
      }
    } catch (e) {
      print('Error checking if user favorited news: $e');
    }
  }

  Future<void> _checkIfUserFavoritedEvents(String newsId) async {
    if (_userId == null || newsId.isEmpty) return;

    try {
      final response =
          await ApiService.checkIfUserFavoritedEvent(newsId, _userId!);
      if (response.statusCode == 200) {
        setState(() {
          _eventFavoriteStates[newsId] = response.data['is_favorited'] ?? false;
        });
      }
    } catch (e) {
      print('Error checking if user favorited event: $e');
    }
  }

  Future<void> _toggleEventFav(String eventId) async {
    if (_isFaving) return;

    setState(() {
      _isFav = true;
    });

    try {
      final currentState = _eventFavoriteStates[eventId] ?? false;
      if (currentState) {
        final response = await ApiService.removeEventFromUserFavorites(
          eventId,
          _userId!,
        );

        if (response.statusCode == 200) {
          setState(() {
            _eventFavoriteStates[eventId] = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Event removed from favorite!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to unfav event',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
        ;
      } else {
        final response = await ApiService.addEventToUserFavorites(
          eventId,
          _userId!,
        );

        if (response.statusCode == 200) {
          setState(() {
            _eventFavoriteStates[eventId] = true;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Event added to fav!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to fav event',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
        ;
      }
    } catch (e) {
      setState(() {
        _isFav = false;
      });
    }
  }

  Future<void> _toggleLike(String eventId) async {
    if (_isLiking) return;

    setState(() {
      _isLiking = true;
    });

    try {
      final currentState = _eventLikeStates[eventId] ?? false;
      if (currentState) {
        final response = await ApiService.userToUnLikeEvent(
          eventId,
          _userId!,
        );

        if (response.statusCode == 200) {
          setState(() {
            _eventLikeStates[eventId] = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Event unliked!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to unlike event',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
        ;
      } else {
        final response = await ApiService.userToLikeEvent(
          eventId,
          _userId!,
        );

        if (response.statusCode == 200) {
          setState(() {
            _eventLikeStates[eventId] = true;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Event liked!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to like event',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
        ;
      }
    } catch (e) {
      setState(() {
        _isLiking = false;
      });
    }
  }

  Widget _buildSearchAndFilter() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Search bar
          Expanded(
            flex: 3,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search...',
                suffixIcon: IconButton(
                  icon: Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _performSearch();
                  },
                ),
                border: InputBorder.none,
              ),
              onSubmitted: (value) => _performSearch(),
            ),
          ),

          SizedBox(width: 10),

          // Filter button
          ElevatedButton.icon(
            onPressed: _showFilterDialog,
            icon: Icon(Icons.filter_list, size: 18),
            label: Text('Filter'),
            style: ElevatedButton.styleFrom(
              foregroundColor: Color(0xFF1F41BB),
              backgroundColor: Colors.white,
              side: BorderSide(color: Color(0xFF1F41BB)),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataList() {
    switch (selectedButton) {
      case 0:
        return _buildJobsList();
      case 1:
        return _buildNewsList();
      case 2:
        return _buildEventsList();
      default:
        return Container();
    }
  }

  Widget _buildJobsList() {
    if (_isLoadingJobs && _jobs.isEmpty) {
      return Center(child: CircularProgressIndicator());
    }

    if (_jobs.isEmpty) {
      return Center(
        child: Text(
          'No jobs found.',
          style: TextStyle(fontFamily: "Satoshi", fontSize: 16),
        ),
      );
    }

    return Column(
      children: [
        ..._jobs.map((job) => _buildJobCard(job)).toList(),
        if (_isLoadingMore)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }

  Widget _buildNewsList() {
    if (_isLoadingNews && _news.isEmpty) {
      return Center(child: CircularProgressIndicator());
    }

    if (_news.isEmpty) {
      return Center(
        child: Text(
          'No news found.',
          style: TextStyle(fontFamily: "Satoshi", fontSize: 16),
        ),
      );
    }

    return Column(
      children: [
        ..._news.map((news) => _buildNewsCard(news)).toList(),
        if (_isLoadingMore)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }

  Widget _buildEventsList() {
    if (_isLoadingEvents && _events.isEmpty) {
      return Center(child: CircularProgressIndicator());
    }

    if (_events.isEmpty) {
      return Center(
        child: Text(
          'No events found.',
          style: TextStyle(fontFamily: "Satoshi", fontSize: 16),
        ),
      );
    }

    return Column(
      children: [
        ..._events.map((event) => _buildEventCard(event)).toList(),
        if (_isLoadingMore)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }

  Widget _buildJobCard(dynamic job) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 5.0),
      child: Card(
        elevation: 2,
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        child: InkWell(
          onTap: () {
            // Navigate to job details
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: job['company_logo'] != null
                            ? NetworkImage(job['company_logo'])
                            : AssetImage('assets/event.jpg') as ImageProvider,
                        fit: BoxFit.cover,
                      ),
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            job['title'] ?? 'Job Title',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontFamily: "Satoshi",
                              fontSize: 16,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 4),
                          Text(
                            job['company_name'] ?? 'Company',
                            style: TextStyle(
                              color: Colors.black54,
                              fontFamily: "Satoshi",
                              fontSize: 14,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            job['location'] ?? 'Location',
                            style: TextStyle(
                              color: Colors.black54,
                              fontFamily: "Satoshi",
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      job['job_type'] ?? 'Full-time',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1F41BB),
                        fontFamily: "Satoshi",
                      ),
                    ),
                    Text(
                      job['salary'] ?? 'Salary not specified',
                      style: TextStyle(
                        color: Colors.black54,
                        fontFamily: "Satoshi",
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNewsCard(dynamic news) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 5.0),
      child: Card(
        elevation: 2,
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    NewsDetailsScreen(news: news, userId: _userId!),
              ),
            );
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: news['image_url'] != null
                            ? NetworkImage(news['image_url'])
                            : AssetImage('assets/event.jpg') as ImageProvider,
                        fit: BoxFit.cover,
                      ),
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            news['title'] ?? 'News Title',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontFamily: "Satoshi",
                              fontSize: 16,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 4),
                          Text(
                            news['story'] ?? 'No description available',
                            style: TextStyle(
                              color: Colors.black54,
                              fontFamily: "Satoshi",
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      news['category'] ?? 'News',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1F41BB),
                        fontFamily: "Satoshi",
                      ),
                    ),
                    Row(
                      children: [
                        Text(
                          'By ${news['author'] ?? 'Unknown'}',
                          style: TextStyle(
                            color: Colors.black54,
                            fontFamily: "Satoshi",
                            fontSize: 12,
                          ),
                        ),
                        SizedBox(width: 5),
                        GestureDetector(
                          onTap: () => _toggleNewsFavorite(news['id']),
                          child: isLoading
                              ? SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white),
                                  ),
                                )
                              : Icon(
                                  _newsFavoriteStates[news['id']] ?? false
                                      ? Icons.favorite
                                      : Icons.favorite_border,
                                  color: Color(0xFF1F41BB),
                                ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEventCard(dynamic event) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 5.0),
      child: Card(
        elevation: 2,
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    EventDetailsScreen(event: event, userId: _userId!),
              ),
            );
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: event['image_url'] != null
                            ? NetworkImage(event['image_url'])
                            : AssetImage('assets/event.jpg') as ImageProvider,
                        fit: BoxFit.cover,
                      ),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Stack(children: [
                      Positioned(
                        top: 1,
                        left: 0,
                        child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            //   decoration: BoxDecoration(
                            //     color: Color(0xFFFF6B35),
                            //     borderRadius: BorderRadius.circular(20),
                            //   ),
                            child: GestureDetector(
                              onTap: () => _toggleEventFav(event['id']),
                              child: Icon(
                                _eventFavoriteStates[event['id']] ?? false
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color:
                                    _eventFavoriteStates[event['id']] ?? false
                                        ? Colors.red
                                        : Colors.grey,
                              ),
                            )),
                      ),
                    ]),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            event['title'] ?? 'Event Title',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontFamily: "Satoshi",
                              fontSize: 16,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 4),
                          Text(
                            event['short_desc'] ?? 'No description available',
                            style: TextStyle(
                              color: Colors.black54,
                              fontFamily: "Satoshi",
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      event['category_name'] ?? 'Event',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1F41BB),
                        fontFamily: "Satoshi",
                      ),
                    ),
                    Text(
                      //   formatEventDateTime(
                      //       event['event_date'], event['event_time']),
                      event['event_date'],
                      style: TextStyle(
                        color: Colors.black54,
                        fontFamily: "Satoshi",
                        fontSize: 12,
                      ),
                    ),
                    SizedBox(width: 10),
                    GestureDetector(
                      onTap: () => _toggleLike(event['id']),
                      child: _isLiking
                          ? SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Icon(
                              _eventLikeStates[event['id']] ?? false
                                  ? Icons.thumb_up
                                  : Icons.thumb_down,
                              color: _eventLikeStates[event['id']] ?? false
                                  ? Colors.blue
                                  : Colors.grey,
                            ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        title: Text(
          'Discover',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row of Buttons
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildCategoryButton(0, 'Jobs', 'assets/bar.svg'),
                  SizedBox(width: 10),
                  _buildCategoryButton(1, 'News', 'assets/fire.svg'),
                  SizedBox(width: 10),
                  _buildCategoryButton(2, 'Events', 'assets/airplay.svg'),
                ],
              ),
            ),
          ),

          // Search and Filter Section
          _buildSearchAndFilter(),

          // Dynamic Content List
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              child: _buildDataList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryButton(int index, String title, String iconPath) {
    return ElevatedButton(
      onPressed: () => onButtonPressed(index),
      style: ElevatedButton.styleFrom(
        foregroundColor:
            selectedButton == index ? Colors.white : Color(0xFF1F41BB),
        backgroundColor:
            selectedButton == index ? Color(0xFF1F41BB) : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        side: BorderSide(color: Color(0xFF1F41BB)),
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(iconPath, width: 20, height: 20),
          SizedBox(width: 8),
          Text(title),
        ],
      ),
    );
  }
}

class FilterBottomSheet extends StatefulWidget {
  final int selectedButton;
  final List<dynamic> jobTypes;
  final List<dynamic> newsCategories;
  final List<dynamic> eventCategories;
  final Set<String> selectedJobTypes;
  final Set<String> selectedCategoryIds;
  final bool isLoadingJobTypes;
  final bool isLoadingNewsCategories;
  final bool isLoadingEventCategories;
  final Function(Set<String>, Set<String>) onApplyFilters;
  final VoidCallback onClearFilters;

  const FilterBottomSheet({
    Key? key,
    required this.selectedButton,
    required this.jobTypes,
    required this.newsCategories,
    required this.eventCategories,
    required this.selectedJobTypes,
    required this.selectedCategoryIds,
    required this.isLoadingJobTypes,
    required this.isLoadingNewsCategories,
    required this.isLoadingEventCategories,
    required this.onApplyFilters,
    required this.onClearFilters,
  }) : super(key: key);

  @override
  _FilterBottomSheetState createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  late Set<String> _localSelectedJobTypes;
  late Set<String> _localSelectedCategoryIds;

  @override
  void initState() {
    super.initState();
    _localSelectedJobTypes = Set.from(widget.selectedJobTypes);
    _localSelectedCategoryIds = Set.from(widget.selectedCategoryIds);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: EdgeInsets.only(top: 10),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filters',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    fontFamily: "Satoshi",
                  ),
                ),
                TextButton(
                  onPressed: widget.onClearFilters,
                  child: Text(
                    'Clear All',
                    style: TextStyle(
                      color: Color(0xFF1F41BB),
                      fontFamily: "Satoshi",
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Filter content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.selectedButton == 0) _buildJobTypesSection(),
                  if (widget.selectedButton == 1) _buildNewsCategoriesSection(),
                  if (widget.selectedButton == 2)
                    _buildEventCategoriesSection(),
                ],
              ),
            ),
          ),

          // Apply button
          Padding(
            padding: EdgeInsets.all(20),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => widget.onApplyFilters(
                    _localSelectedJobTypes, _localSelectedCategoryIds),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color(0xFF1F41BB),
                  padding: EdgeInsets.symmetric(vertical: 15),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: Text(
                  'Apply Filters',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    fontFamily: "Satoshi",
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildJobTypesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Job Types',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            fontFamily: "Satoshi",
          ),
        ),
        SizedBox(height: 10),
        if (widget.isLoadingJobTypes)
          Center(child: CircularProgressIndicator())
        else
          ...widget.jobTypes.map((jobType) {
            final jobTypeString = jobType.toString();
            final isSelected = _localSelectedJobTypes.contains(jobTypeString);
            return CheckboxListTile(
              title: Text(
                jobTypeString,
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              value: isSelected,
              onChanged: (bool? value) {
                print(
                    'Job type checkbox changed: $jobTypeString, value: $value');
                setState(() {
                  if (value == true) {
                    _localSelectedJobTypes.add(jobTypeString);
                    print(
                        'Added $jobTypeString. Selected: $_localSelectedJobTypes');
                  } else {
                    _localSelectedJobTypes.remove(jobTypeString);
                    print(
                        'Removed $jobTypeString. Selected: $_localSelectedJobTypes');
                  }
                });
              },
              activeColor: Color(0xFF1F41BB),
              controlAffinity: ListTileControlAffinity.leading,
            );
          }).toList(),
        SizedBox(height: 20),
      ],
    );
  }

  Widget _buildNewsCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'News Categories',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            fontFamily: "Satoshi",
          ),
        ),
        SizedBox(height: 10),
        if (widget.isLoadingNewsCategories)
          Center(child: CircularProgressIndicator())
        else
          ...widget.newsCategories.map((category) {
            final categoryId = category['id'] ?? '';
            final categoryName = category['name'] ?? 'Unknown';
            final isSelected = _localSelectedCategoryIds.contains(categoryId);
            return CheckboxListTile(
              title: Text(
                categoryName,
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              value: isSelected,
              onChanged: (bool? value) {
                print(
                    'News category checkbox changed: $categoryName, value: $value');
                setState(() {
                  if (value == true) {
                    _localSelectedCategoryIds.add(categoryId);
                    print(
                        'Added $categoryName. Selected: $_localSelectedCategoryIds');
                  } else {
                    _localSelectedCategoryIds.remove(categoryId);
                    print(
                        'Removed $categoryName. Selected: $_localSelectedCategoryIds');
                  }
                });
              },
              activeColor: Color(0xFF1F41BB),
              controlAffinity: ListTileControlAffinity.leading,
            );
          }).toList(),
        SizedBox(height: 20),
      ],
    );
  }

  Widget _buildEventCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Event Categories',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            fontFamily: "Satoshi",
          ),
        ),
        SizedBox(height: 10),
        if (widget.isLoadingEventCategories)
          Center(child: CircularProgressIndicator())
        else
          ...widget.eventCategories.map((category) {
            final categoryId = category['id'] ?? '';
            final categoryName = category['name'] ?? 'Unknown';
            final isSelected = _localSelectedCategoryIds.contains(categoryId);
            return CheckboxListTile(
              title: Text(
                categoryName,
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              value: isSelected,
              onChanged: (bool? value) {
                print(
                    'Event category checkbox changed: $categoryName, value: $value');
                setState(() {
                  if (value == true) {
                    _localSelectedCategoryIds.add(categoryId);
                    print(
                        'Added $categoryName. Selected: $_localSelectedCategoryIds');
                  } else {
                    _localSelectedCategoryIds.remove(categoryId);
                    print(
                        'Removed $categoryName. Selected: $_localSelectedCategoryIds');
                  }
                });
              },
              activeColor: Color(0xFF1F41BB),
              controlAffinity: ListTileControlAffinity.leading,
            );
          }).toList(),
        SizedBox(height: 20),
      ],
    );
  }
}
