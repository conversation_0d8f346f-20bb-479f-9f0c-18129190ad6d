import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import '../../services/api_service.dart';

class EventDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> event;
  final String userId;

  const EventDetailsScreen(
      {Key? key, required this.event, required this.userId})
      : super(key: key);

  @override
  State<EventDetailsScreen> createState() => _EventDetailsScreenState();
}

class _EventDetailsScreenState extends State<EventDetailsScreen> {
  bool _isLiked = false;
  int _likesCount = 0;
  bool _isLiking = false;
  bool _isFaved = false;
  bool _isFaving = false;

  @override
  void initState() {
    super.initState();
    _likesCount = widget.event['likes_count'] ?? 0;
    _checkIfUserLikedEvent();
    _checkIfUserFavoritedEvent();
  }

  // Check if user has already liked the event
  Future<void> _checkIfUserLikedEvent() async {
    try {
      final response = await ApiService.checkIfUserLikedEvent(
        widget.event['id'],
        widget.userId,
      );

      if (response.statusCode == 200) {
        setState(() {
          _isLiked = response.data['is_liked'] ?? false;
        });
      }
    } catch (e) {
      print('Error checking if user liked event: $e');
    }
  }

// check if user already favorited event

  Future<void> _checkIfUserFavoritedEvent() async {
    try {
      final response = await ApiService.checkIfUserFavoritedEvent(
        widget.event['id'],
        widget.userId,
      );

      if (response.statusCode == 200) {
        setState(() {
          _isFaved = response.data['is_favorited'] ?? false;
        });
      }
    } catch (e) {
      print('Error checking if user favorited event: $e');
    }
  }

// user to favorite event
  Future<void> _toggleFav() async {
    if (_isFaving) return;

    setState(() {
      _isFaving = true;
    });

    try {
      if (_isFaved) {
        final response = await ApiService.removeEventFromUserFavorites(
          widget.event['id'],
          widget.userId,
        );

        if (response.statusCode == 200) {
          setState(() {
            _isFaved = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Event removed from favorite!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to remove from favorites',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
        ;
      } else {
        final response = await ApiService.addEventToUserFavorites(
          widget.event['id'],
          widget.userId,
        );

        if (response.statusCode == 200) {
          setState(() {
            _isFaved = true;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Event added to favorites!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to add to favorites',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
        ;
      }
    } catch (e) {
      setState(() {
        _isFaving = false;
      });
    }

  }


  Future<void> _toggleLike() async {
    if (_isLiking) return;

    setState(() {
      _isLiking = true;
    });

    try {
      if (_isLiked) {
        final response = await ApiService.userToUnLikeEvent(
          widget.event['id'],
          widget.userId,
        );

        if (response.statusCode == 200) {
          setState(() {
            _isLiked = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Event unliked!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to unlike event',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
        ;
      } else {
        final response = await ApiService.userToLikeEvent(
          widget.event['id'],
          widget.userId,
        );

        if (response.statusCode == 200) {
          setState(() {
            _isLiked = true;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Event liked!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to like event',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
        ;
      }
    } catch (e) {
      setState(() {
        _isLiking = false;
      });
    }
  }

  void _shareEvent() {
    // Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Share functionality coming soon!',
          style: TextStyle(fontFamily: "Satoshi"),
        ),
        backgroundColor: Color(0xFF1F41BB),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final event = widget.event;
    final rawDate = event['event_date'];
    DateTime? parsedDate;

    try {
      parsedDate = DateTime.parse(rawDate);
    } catch (e) {
      parsedDate = null;
    }

    final day = parsedDate != null ? DateFormat('d').format(parsedDate) : '??';
    final month = parsedDate != null
        ? DateFormat('MMM').format(parsedDate).toUpperCase()
        : '???';

    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        slivers: [
          // App Bar with Image
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            backgroundColor: Color(0xFF1F41BB),
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => Navigator.pop(context),
            ),
            actions: [
              // IconButton(
              //   icon: Icon(Icons.share, color: Colors.white),
              //   onPressed: _shareEvent,
              // ),
              Container(
                padding: EdgeInsets.all(8),
                margin: EdgeInsets.only(
                  right: 10,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Text(
                      day,
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF6366F1),
                        fontFamily: "Satoshi",
                      ),
                    ),
                    Text(
                      month,
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: Colors.black54,
                        fontFamily: "Satoshi",
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                height: 140,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  image: event['image_url'] != null &&
                          event['image_url'].isNotEmpty
                      ? DecorationImage(
                          image: NetworkImage(event['image_url']),
                          fit: BoxFit.cover,
                          onError: (error, stackTrace) {},
                        )
                      : null,
                  gradient:
                      event['image_url'] == null || event['image_url'].isEmpty
                          ? LinearGradient(
                              colors: [
                                Color(0xFF6366F1),
                                Color(0xFF8B5CF6),
                                Color(0xFFEC4899),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            )
                          : null,
                ),
                child: Stack(
                  children: [
                    // Category badge
                    Positioned(
                      bottom: 12,
                      left: 12,
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Color(0xFFFF6B35),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          (event['category_name'] ?? 'N/A').toUpperCase(),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            fontFamily: "Satoshi",
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 12,
                      right: 12,
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        // decoration: BoxDecoration(
                        //   color: Colors.black.withOpacity(0.6),
                        //   borderRadius: BorderRadius.circular(8),
                        // ),
                        child: Row(
                          children: [
                            // Message button with count
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.5),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.message,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                  SizedBox(width: 4),
                                  Text(
                                    (event['opinion_count'] ?? 0)
                                        .toString(), // Replace with your actual message count
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 8),
                            // Like button with count
                            GestureDetector(
                              onTap: _isLiking ? null : _toggleLike,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.5),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    _isLiking
                                        ? SizedBox(
                                            width: 16,
                                            height: 16,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                      Colors.white),
                                            ),
                                          )
                                        : Icon(
                                            _isLiked
                                                ? Icons.thumb_up
                                                : Icons.thumb_down,
                                            color: _isLiked
                                                ? Colors.red
                                                : Colors.white,
                                            size: 16,
                                          ),
                                    SizedBox(width: 4),
                                    Text(
                                      _likesCount.toString() ?? "0",
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),

                                  ],
                                ),
                              ),
                            ),
                            SizedBox(width: 8),
                            // Favourite button
                            GestureDetector(
                              onTap: _isFaving ? null : _toggleFav,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.5),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    _isFaving
                                        ? SizedBox(
                                            width: 16,
                                            height: 16,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                      Colors.white),
                                            ),
                                          )
                                        : Icon(
                                            _isFaved
                                                ? Icons.favorite
                                                : Icons.favorite_border,
                                            color: _isFaved
                                                ? Colors.red
                                                : Colors.white,
                                            size: 16,
                                          ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Event Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.event['title'] ?? 'Event Title',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                fontFamily: "Satoshi",
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                  _buildDetailSection(
                    // title: 'Description',
                    // icon: Icons.description,
                    content: widget.event['description'] ??
                        'No description available',
                  ),
                  SizedBox(height: 30),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String count,
    required String label,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: Color(0xFF1F41BB),
          size: 20,
        ),
        SizedBox(width: 5),
        Text(
          count,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            fontFamily: "Satoshi",
          ),
        ),
        SizedBox(width: 5),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontFamily: "Satoshi",
          ),
        ),
      ],
    );
  }

  Widget _buildDetailSection({
    required String content,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 2.0),
          child: Text(
            content,
            style: TextStyle(
              fontSize: 14,
              color: Colors.black54,
              fontFamily: "Satoshi",
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }
}
