import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../services/api_service.dart';

class NewsDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> news;
  final String userId;

  const NewsDetailsScreen({Key? key, required this.news, required this.userId})
      : super(key: key);

  @override
  State<NewsDetailsScreen> createState() => _NewsDetailsScreenState();
}

class _NewsDetailsScreenState extends State<NewsDetailsScreen> {
  bool isFavorite = false;
  bool isLoading = false;

  String formatNewsDate(String? dateString) {
    if (dateString == null) return 'Date unavailable';

    try {
      final date = DateTime.parse(dateString);
      return DateFormat('MMMM d, y \'at\' h:mm a').format(date);
    } catch (e) {
      return 'Invalid date';
    }
  }

  Future<void> _toggleFavorite() async {
    if (isLoading) return; // Prevent multiple calls

    setState(() {
      isLoading = true;
    });

    try {
      if (isFavorite) {
        // Remove from favorites
        final response = await ApiService.removeNewsFromUserFavorites(
          widget.news['id'],
          widget.userId,
        );

        if (response.statusCode == 200) {
          setState(() {
            isFavorite = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Removed from favourites!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to remove from favourites',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        // Add to favorites
        final response = await ApiService.addNewsToUserFavorites(
          widget.news['id'],
          widget.userId,
        );

        if (response.statusCode == 200) {
          setState(() {
            isFavorite = true;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Added to favourites!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to add to favourites',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'An error occurred. Please try again.',
            style: TextStyle(fontFamily: "Satoshi"),
          ),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final news = widget.news;
    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        slivers: [
          // App Bar with Image
          SliverAppBar(
            expandedHeight: 300.0,
            floating: false,
            pinned: true,
            backgroundColor: Color(0xFF1F41BB),
            leading: IconButton(
              icon: Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.arrow_back,
                  color: Colors.white,
                ),
              ),
              onPressed: () => Navigator.pop(context),
            ),
            actions: [
              IconButton(
                icon: Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: isLoading
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Icon(
                          isFavorite ? Icons.favorite : Icons.favorite_border,
                          color: isFavorite ? Colors.red : Colors.white,
                        ),
                ),
                onPressed: isLoading ? null : _toggleFavorite,
              ),
              SizedBox(width: 16),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  image:
                      news['image_url'] != null && news['image_url'].isNotEmpty
                          ? DecorationImage(
                              image: NetworkImage(news['image_url']),
                              fit: BoxFit.cover,
                              onError: (error, stackTrace) {},
                            )
                          : null,
                  gradient:
                      news['image_url'] == null || news['image_url'].isEmpty
                          ? LinearGradient(
                              colors: [
                                Color(0xFF6366F1),
                                Color(0xFF8B5CF6),
                                Color(0xFFEC4899),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            )
                          : null,
                ),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.3),
                      ],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Category badge
                      Positioned(
                        bottom: 80,
                        left: 20,
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: Color(0xFFFF6B35),
                            borderRadius: BorderRadius.circular(25),
                          ),
                          child: Text(
                            (news['category'] ?? 'News').toUpperCase(),
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              fontFamily: "Satoshi",
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    news['title'] ?? 'News Title',
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      fontFamily: "Satoshi",
                      color: Colors.black87,
                      height: 1.3,
                    ),
                  ),

                  SizedBox(height: 16),

                  // Author and Date info
                  Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 20,
                          backgroundColor: Color(0xFF1F41BB),
                          child: Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                news['author'] ?? 'Unknown Author',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: "Satoshi",
                                  color: Colors.black87,
                                ),
                              ),
                              SizedBox(height: 4),
                              Row(
                                children: [
                                  Icon(
                                    Icons.access_time,
                                    size: 14,
                                    color: Colors.grey[600],
                                  ),
                                  SizedBox(width: 4),
                                  Text(
                                    formatNewsDate(news['created_at']),
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontFamily: "Satoshi",
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 24),

                  // Story Content
                  Container(
                    padding: EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 10,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Story',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            fontFamily: "Satoshi",
                            color: Color(0xFF1F41BB),
                          ),
                        ),
                        SizedBox(height: 16),
                        Text(
                          news['story'] ?? 'No story content available.',
                          style: TextStyle(
                            fontSize: 16,
                            fontFamily: "Satoshi",
                            color: Colors.black87,
                            height: 1.6,
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 24),

                  // Action Buttons
                  // Row(
                  //   children: [
                  //     Expanded(
                  //       child: ElevatedButton.icon(
                  //         onPressed: () {
                  //           // Implement bookmark functionality
                  //           ScaffoldMessenger.of(context).showSnackBar(
                  //             SnackBar(
                  //               content: Text('News bookmarked!'),
                  //               backgroundColor: Color(0xFF1F41BB),
                  //             ),
                  //           );
                  //         },
                  //         icon: Icon(Icons.bookmark_border),
                  //         label: Text(
                  //           'Bookmark',
                  //           style: TextStyle(
                  //             fontFamily: "Satoshi",
                  //             fontWeight: FontWeight.w600,
                  //           ),
                  //         ),
                  //         style: ElevatedButton.styleFrom(
                  //           backgroundColor: Color(0xFF1F41BB),
                  //           foregroundColor: Colors.white,
                  //           padding: EdgeInsets.symmetric(vertical: 12),
                  //           shape: RoundedRectangleBorder(
                  //             borderRadius: BorderRadius.circular(12),
                  //           ),
                  //         ),
                  //       ),
                  //     ),
                  //     SizedBox(width: 12),
                  //     Expanded(
                  //       child: OutlinedButton.icon(
                  //         onPressed: () {
                  //           // Implement share functionality
                  //           ScaffoldMessenger.of(context).showSnackBar(
                  //             SnackBar(
                  //               content: Text('Sharing news...'),
                  //               backgroundColor: Colors.grey[700],
                  //             ),
                  //           );
                  //         },
                  //         icon: Icon(Icons.share),
                  //         label: Text(
                  //           'Share',
                  //           style: TextStyle(
                  //             fontFamily: "Satoshi",
                  //             fontWeight: FontWeight.w600,
                  //           ),
                  //         ),
                  //         style: OutlinedButton.styleFrom(
                  //           foregroundColor: Color(0xFF1F41BB),
                  //           side: BorderSide(color: Color(0xFF1F41BB)),
                  //           padding: EdgeInsets.symmetric(vertical: 12),
                  //           shape: RoundedRectangleBorder(
                  //             borderRadius: BorderRadius.circular(12),
                  //           ),
                  //         ),
                  //       ),
                  //     ),
                  //   ],
                  // ),

                  // SizedBox(height: 32),

                  // Additional Info Card
                  // Container(
                  //   width: double.infinity,
                  //   padding: EdgeInsets.all(20),
                  //   decoration: BoxDecoration(
                  //     gradient: LinearGradient(
                  //       colors: [
                  //         Color(0xFF1F41BB).withOpacity(0.1),
                  //         Color(0xFF6366F1).withOpacity(0.1),
                  //       ],
                  //       begin: Alignment.topLeft,
                  //       end: Alignment.bottomRight,
                  //     ),
                  //     borderRadius: BorderRadius.circular(16),
                  //     border: Border.all(
                  //       color: Color(0xFF1F41BB).withOpacity(0.2),
                  //     ),
                  //   ),
                  //   child: Column(
                  //     crossAxisAlignment: CrossAxisAlignment.start,
                  //     children: [
                  //       Row(
                  //         children: [
                  //           Icon(
                  //             Icons.info_outline,
                  //             color: Color(0xFF1F41BB),
                  //             size: 20,
                  //           ),
                  //           SizedBox(width: 8),
                  //           Text(
                  //             'News Information',
                  //             style: TextStyle(
                  //               fontSize: 16,
                  //               fontWeight: FontWeight.bold,
                  //               fontFamily: "Satoshi",
                  //               color: Color(0xFF1F41BB),
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //       SizedBox(height: 12),
                  //       _buildInfoRow('News ID', news['id'] ?? 'N/A'),
                  //       _buildInfoRow(
                  //           'Category', news['category'] ?? 'General'),
                  //       _buildInfoRow('Status', news['status'] ?? 'Active'),
                  //       _buildInfoRow(
                  //           'Published', formatNewsDate(news['created_at'])),
                  //       if (news['updated_at'] != news['created_at'])
                  //         _buildInfoRow('Last Updated',
                  //             formatNewsDate(news['updated_at'])),
                  //     ],
                  //   ),
                  // ),

                  // SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14,
                fontFamily: "Satoshi",
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontFamily: "Satoshi",
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
