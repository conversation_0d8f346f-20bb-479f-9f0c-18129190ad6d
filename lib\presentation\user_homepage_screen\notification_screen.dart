import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

void main() => runApp(MyApp());

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: NotificationsScreen(),
    );
  }
}

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () {
            // Back Action
          },
        ),
        centerTitle: true,
        title: const Text(
          "Notifications",
          style: TextStyle(
              color: Colors.black,
              fontFamily: "Satoshi",
              fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert, color: Colors.black),
            onPressed: () {
              // Options Action
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: const [
            NotificationCard(
              backgroundColor: Color(0xFF1F41BB),
              color: Colors.white,
              svgIcon: 'assets/notification_icon_one.svg',
              title: 'New Job Posted',
              description: 'A new job has been posted.',
              timeAgo: '10hrs ago',
            ),
            SizedBox(height: 16),
            NotificationCard(
              backgroundColor: Colors.white,
              color: Colors.black,
              svgIcon: 'assets/notification_icon_two.svg',
              title: 'Interview Scheduled',
              description: 'Your interview is scheduled today.',
              timeAgo: '2 days ago',
            ),
            SizedBox(height: 16),
            NotificationCard(
              backgroundColor: Colors.white,
              color: Colors.black,
              svgIcon: 'assets/notification_icon_three.svg',
              title: 'Profile Update Reminder',
              description: 'Please update your profile.',
              timeAgo: '3 days ago',
            ),
            SizedBox(height: 16),
            NotificationCard(
              backgroundColor: Colors.white,
              color: Colors.black,
              svgIcon: 'assets/notification_icon_four.svg',
              title: 'New Message',
              description: 'You have a new message!.',
              timeAgo: '5 days ago',
            ),
            SizedBox(height: 16),
            NotificationCard(
              backgroundColor: Colors.white,
              color: Colors.black,
              svgIcon: 'assets/notification_icon_four.svg',
              title: 'Application Status Update',
              description: 'Application status updated.',
              timeAgo: '1 week ago',
            ),
          ],
        ),
      ),
    );
  }
}

class NotificationCard extends StatelessWidget {
  final Color backgroundColor;
  final Color color;
  final String svgIcon;
  final String title;
  final String description;

  final String timeAgo;

  const NotificationCard({
    super.key,
    required this.backgroundColor,
    required this.color,
    required this.svgIcon,
    required this.title,
    required this.description,
    required this.timeAgo,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // First Row with SVG Icon, Title, and Description
          Row(
            children: [
              SvgPicture.asset(
                svgIcon,
                height: 40,
                width: 40,
              ),
              const SizedBox(width: 10),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontFamily: "Satoshi",
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: backgroundColor == Colors.blue
                          ? Colors.white
                          : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontFamily: "Satoshi",
                      fontSize: 14,
                      color: backgroundColor == Colors.blue
                          ? Colors.white70
                          : Colors.black54,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                timeAgo,
                style: TextStyle(
                  fontSize: 12,
                  color: backgroundColor == Colors.blue
                      ? Colors.white70
                      : Colors.black54,
                ),
              ),
              GestureDetector(
                onTap: () {
                  // Handle Mark as Read
                },
                child: const Text(
                  'Mark as Read',
                  style: TextStyle(
                    fontFamily: "Satoshi",
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
