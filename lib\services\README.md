# API Service Documentation

This directory contains the API service layer for the Tareek Flutter application, built using Dio HTTP client with custom interceptors.

## Files Overview

### 1. `api_endpoints.dart`
Contains all API endpoint constants and helper methods for building URLs.
- Base URL: `https://taraq-go.onrender.com`
- API version: `/api/v1`
- Organized endpoints for authentication, users, jobs, notifications, etc.

### 2. `api_interceptor.dart`
Custom Dio interceptor that handles:
- **Authentication**: Automatically adds Bearer token to requests
- **Logging**: Detailed request/response logging in debug mode
- **Error Handling**: Converts HTTP errors to user-friendly messages
- **Headers**: Adds common headers (Content-Type, Accept)

### 3. `api_client.dart`
Singleton Dio client with:
- **Base Configuration**: Timeouts, base URL, headers
- **HTTP Methods**: GET, POST, PUT, PATCH, DELETE
- **File Upload**: Support for multipart file uploads
- **Token Management**: Set/clear authentication tokens
- **Pretty Logging**: Enhanced logging in debug mode

### 4. `api_service.dart`
High-level service layer with methods for:
- **Authentication**: login, register, logout, forgot password, OTP verification
- **User Management**: profile operations, password changes
- **Job Operations**: CRUD operations, search, apply
- **Notifications**: fetch and mark as read
- **Favorites**: manage favorite jobs

### 5. `api_example_usage.dart`
Example implementations showing how to:
- Handle API responses
- Manage loading states
- Handle errors gracefully
- Extract user-friendly error messages

### 6. `api_integration_example.dart`
Complete Flutter widget demonstrating:
- API integration in UI
- Loading states management
- Error handling with user feedback
- Real-time data display

## Quick Start

### 1. Basic Usage

```dart
import 'package:tareek/services/api_service.dart';

// Login
try {
  final response = await ApiService.login(
    email: '<EMAIL>',
    password: 'password123',
  );
  // Handle success
} on DioException catch (e) {
  // Handle error
  print('Error: ${e.error}');
}
```

### 2. Setting Authentication Token

```dart
import 'package:tareek/services/api_client.dart';

// Set token manually (usually done automatically after login)
ApiClient.instance.setAuthToken('your_jwt_token_here');

// Clear token (logout)
ApiClient.instance.clearAuthToken();
```

### 3. Making Custom API Calls

```dart
import 'package:tareek/services/api_client.dart';

// Direct API client usage
final response = await ApiClient.instance.get('/custom/endpoint');
```

## Error Handling

The API service provides comprehensive error handling:

### DioException Types
- **Connection Timeout**: Network timeout errors
- **Connection Error**: No internet connection
- **Bad Response**: HTTP error responses (400, 401, 404, 500, etc.)

### User-Friendly Messages
The interceptor automatically converts technical errors to user-friendly messages:
- 401 → "Please login to continue"
- 404 → "Resource not found"
- 500 → "Server error. Please try again later"

## Authentication Flow

1. **Login**: Call `ApiService.login()` - token is automatically stored
2. **Authenticated Requests**: Token is automatically added to all subsequent requests
3. **Token Expiry**: 401 responses automatically clear the stored token
4. **Logout**: Call `ApiService.logout()` - token is automatically cleared

## Configuration

### Timeouts
- Connect timeout: 30 seconds
- Receive timeout: 30 seconds
- Send timeout: 30 seconds

### Base URL
- Production: `https://taraq-go.onrender.com/api/v1`
- Can be changed in `api_endpoints.dart`

### Logging
- Enabled in debug mode only
- Includes request/response details
- Pretty formatted output

## Integration in Existing Screens

To integrate the API service in your existing screens:

1. **Import the service**:
```dart
import 'package:tareek/services/api_service.dart';
```

2. **Add loading state**:
```dart
bool _isLoading = false;
```

3. **Make API calls**:
```dart
setState(() => _isLoading = true);
try {
  final response = await ApiService.getJobs();
  // Handle success
} catch (e) {
  // Handle error
} finally {
  setState(() => _isLoading = false);
}
```

## Testing

Use the `api_integration_example.dart` widget to test API functionality:

1. Add it to your app's navigation
2. Test login, job fetching, and search
3. Observe request/response logs in debug console

## Security Notes

- Tokens are stored in memory only (not persisted)
- HTTPS is enforced for all requests
- Sensitive data is not logged in production
- Authentication tokens are automatically cleared on 401 responses

## Customization

### Adding New Endpoints
1. Add endpoint constant to `api_endpoints.dart`
2. Add service method to `api_service.dart`
3. Handle response in your UI

### Custom Headers
Add custom headers in the interceptor or per-request:
```dart
final response = await ApiClient.instance.get(
  '/endpoint',
  options: Options(headers: {'Custom-Header': 'value'}),
);
```

### Request/Response Transformation
Modify the interceptor to transform data as needed for your API format.
