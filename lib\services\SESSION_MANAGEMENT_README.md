# Session Management System

This document describes the persistent session management system implemented for the Tareek Flutter application.

## Overview

The session management system provides:

-   **Persistent Authentication**: Users remain logged in across app restarts
-   **Automatic Token Refresh**: Expired access tokens are automatically refreshed
-   **Secure Storage**: Tokens are stored securely using SharedPreferences
-   **Session Validation**: App startup checks for valid sessions
-   **Seamless UX**: Users only see login screen when session is truly expired

## Components

### 1. SessionManager (`session_manager.dart`)

Central service for managing authentication tokens and user data.

**Key Methods:**

-   `saveSession()` - Store authentication data after login
-   `hasValidSession()` - Check if user has valid session
-   `getAccessToken()` / `getRefreshToken()` - Retrieve stored tokens
-   `updateAccessToken()` - Update access token after refresh
-   `clearSession()` - Clear all session data on logout

### 2. Enhanced API Interceptor (`api_interceptor.dart`)

Dio interceptor that automatically handles token refresh.

**Features:**

-   Detects 401 Unauthorized responses
-   Automatically attempts token refresh using refresh token
-   Retries original request with new access token
-   Clears session if refresh fails

### 3. App Initialization Service (`app_initialization_service.dart`)

Handles app startup logic and session validation.

**Features:**

-   Initializes API client with stored tokens
-   Validates existing sessions
-   Determines initial navigation route
-   Handles initialization errors gracefully

### 4. Enhanced Splash Screen (`splash_screen.dart`)

Updated to check for valid sessions on app startup.

**Flow:**

1. Initialize API client with stored tokens
2. Check for valid session
3. Navigate to home screen if session valid
4. Navigate to onboarding if no valid session

## Usage

### Login Flow

```dart
// Login automatically saves session
final response = await ApiService.login(
  email: email,
  password: password,
);

// Session is automatically saved with:
// - Access token
// - Refresh token
// - User ID
// - User type
// - User data
```

### Automatic Token Refresh

```dart
// Happens automatically in API interceptor
// When API call receives 401:
// 1. Interceptor detects 401 response
// 2. Calls POST /auth/api/refresh-token with refresh token in Authorization header
// 3. Updates stored access token and user data from response
// 4. Retries original request with new access token
// 5. If refresh fails, clears session
```

### Session Validation

```dart
// Check if user has valid session
final sessionManager = SessionManager.instance;
final hasValidSession = await sessionManager.hasValidSession();

if (hasValidSession) {
  // Navigate to home screen
} else {
  // Navigate to login screen
}
```

### Logout

```dart
// Logout clears all session data
await ApiService.logout();
// This automatically:
// - Calls logout API
// - Clears stored tokens
// - Clears session data
```

## App Startup Flow

1. **Splash Screen Loads**
2. **Initialize API Client** with stored tokens
3. **Check Session Validity**
    - If valid session exists → Navigate to appropriate home screen
    - If no valid session → Navigate to onboarding
4. **Handle Errors** → Navigate to onboarding

## Token Refresh Flow

1. **API Call Made** with stored access token
2. **Server Returns 401** (token expired)
3. **Interceptor Detects 401**
4. **Attempt Token Refresh** by calling POST `/auth/api/refresh-token` with refresh token in Authorization header
5. **If Refresh Successful:**
    - Parse refresh response with structure:
        ```json
        {
          "access_token": "new_jwt_token",
          "expires_in": 1748620230,
          "refresh_token": "new_refresh_token",
          "user": { ... },
          "user_id": "user_id",
          "user_type": "job_seeker"
        }
        ```
    - Update stored tokens and user data
    - Calculate expiry from `expires_in` timestamp
    - Retry original API call
6. **If Refresh Fails:**
    - Clear session data
    - User will be redirected to login on next navigation

## Security Considerations

-   Tokens are stored in SharedPreferences (consider flutter_secure_storage for production)
-   Access tokens have 24-hour expiry by default
-   Refresh tokens are used only when access token expires
-   Session is cleared if both tokens are invalid
-   All token operations include error handling

## Configuration

### Token Expiry

Default access token expiry is set to 24 hours. This can be configured in:

-   `SessionManager.saveSession()` - when saving new session
-   `ApiInterceptor._handleTokenRefresh()` - when refreshing token

### API Endpoints

Token refresh endpoint is configured in `UserApiEndpoints.refreshToken`

## Testing

To test the session management:

1. **Login** - Verify tokens are saved
2. **App Restart** - Verify user stays logged in
3. **Token Expiry** - Verify automatic refresh works
4. **Refresh Token Expiry** - Verify user is logged out
5. **Network Errors** - Verify graceful error handling

## Troubleshooting

### User Always Redirected to Login

-   Check if tokens are being saved during login
-   Verify `hasValidSession()` logic
-   Check for errors in app initialization

### Token Refresh Not Working

-   Verify refresh token endpoint is correct
-   Check if refresh token is being stored
-   Verify interceptor is properly configured

### Session Data Corruption

-   Clear app data to reset session
-   Check for proper error handling in session operations
