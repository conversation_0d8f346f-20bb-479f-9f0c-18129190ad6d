import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'user_api_endpoints.dart';
import 'api_interceptor.dart';
import 'session_manager.dart';

/// Singleton API client using Dio for HTTP requests
class ApiClient {
  static ApiClient? _instance;
  late Dio _dio;
  late ApiInterceptor _apiInterceptor;

  // Private constructor
  ApiClient._internal() {
    _apiInterceptor = ApiInterceptor();
    _initializeDio();
  }

  // Singleton instance getter
  static ApiClient get instance {
    _instance ??= ApiClient._internal();
    return _instance!;
  }

  // Dio instance getter
  Dio get dio => _dio;

  // Auth token setter
  void setAuthToken(String? token) {
    _apiInterceptor.setAuthToken(token);
  }

  // Auth token getter
  String? get authToken => _apiInterceptor.authToken;

  // Initialize Dio with configuration
  void _initializeDio() {
    _dio = Dio(BaseOptions(
      baseUrl: UserApiEndpoints.fullBaseUrl,
      connectTimeout: const Duration(seconds: 50),
      receiveTimeout: const Duration(seconds: 50),
      sendTimeout: const Duration(seconds: 50),
      headers: {
        'Multipart': 'application/json',
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(_apiInterceptor);

    // Add pretty logger in debug mode
    if (kDebugMode) {
      _dio.interceptors.add(PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
        maxWidth: 90,
      ));
    }
  }

  // GET request
  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  // POST request
  Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  // PUT request
  Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  // PATCH request
  Future<Response> patch(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.patch(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  // DELETE request
  Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Upload file
  Future<Response> uploadFile(
    String path,
    String filePath, {
    String? fileName,
    Map<String, dynamic>? data,
    ProgressCallback? onSendProgress,
    CancelToken? cancelToken,
  }) async {
    try {
      FormData formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(filePath, filename: fileName),
        ...?data,
      });

      return await _dio.post(
        path,
        data: formData,
        onSendProgress: onSendProgress,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Clear auth token (for logout)
  void clearAuthToken() {
    _apiInterceptor.setAuthToken(null);
  }

  /// Initialize API client with stored session tokens
  Future<void> initializeWithStoredSession() async {
    try {
      final sessionManager = SessionManager.instance;
      final accessToken = await sessionManager.getAccessToken();

      if (accessToken != null) {
        setAuthToken(accessToken);
        if (kDebugMode) {
          print('✅ API client initialized with stored token');
        }
      } else {
        if (kDebugMode) {
          print('ℹ️ No stored token found');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing API client with stored session: $e');
      }
    }
  }
}
