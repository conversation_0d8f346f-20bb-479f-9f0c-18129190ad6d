import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'api_service.dart';

/// Example usage of the API service
class ApiExampleUsage {


/// Example: Login
  static Future<void> exampleLogin() async {
    try {
      final response = await ApiService.login(
        email: '<EMAIL>',
        password: 'password123',
      );

      if (response.statusCode == 200) {
        print('Login successful: ${response.data}');
        // Handle successful login
        // Store token, navigate to home screen
      }
    } on DioException catch (e) {
      print('Login failed: ${e.error}');
      // Handle login error
    } catch (e) {
      print('Unexpected error: $e');
    }
  }

  static Future<void> exampleRegister() async {
    try {
      final response = await ApiService.register(
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'password123',
        userType: 'job_seeker',
      );

      if (response.statusCode == 201) {
        print('Registration successful: ${response.data}');
        // Handle successful registration
        // Navigate to login screen
      }
    } on DioException catch (e) {
      print('Registration failed: ${e.error}');
      // Handle registration error
    } catch (e) {
      print('Unexpected error: $e');
    }
  }


  /// Example: Get user profile
  static Future<void> exampleGetUserProfile() async {
    try {
      final response = await ApiService.getUserProfile();

      if (response.statusCode == 200) {
        print('User profile: ${response.data}');
        // Handle user profile data
        // Update UI with user information
      }
    } on DioException catch (e) {
      print('Failed to fetch profile: ${e.error}');
      // Handle error
      if (e.response?.statusCode == 401) {
        // User is not authenticated, redirect to login
        print('User not authenticated, redirecting to login');
      }
    } catch (e) {
      print('Unexpected error: $e');
    }
  }

  /// Example: Create a job (for recruiters)
  static Future<void> exampleCreateJob() async {
    try {
      final jobData = {
        'title': 'Senior Flutter Developer',
        'description': 'We are looking for an experienced Flutter developer...',
        'company': 'Tech Company Inc.',
        'location': 'Remote',
        'job_type': 'Full-time',
        'category': 'Technology',
        'salary_min': 80000,
        'salary_max': 120000,
        'requirements': [
          '3+ years of Flutter experience',
          'Knowledge of Dart programming language',
          'Experience with REST APIs',
        ],
        'benefits': [
          'Health insurance',
          'Remote work',
          'Flexible hours',
        ],
      };

      final response = await ApiService.createJob(jobData: jobData);

      if (response.statusCode == 201) {
        print('Job created successfully: ${response.data}');
        // Handle successful job creation
        // Navigate to job details or job list
      }
    } on DioException catch (e) {
      print('Failed to create job: ${e.error}');
      // Handle error
    } catch (e) {
      print('Unexpected error: $e');
    }
  }

  /// Example: Apply for a job
  static Future<void> exampleApplyForJob() async {
    try {
      final applicationData = {
        'cover_letter': 'I am very interested in this position...',
        'resume_url': 'https://example.com/resume.pdf',
      };

      final response = await ApiService.applyForJob(
        jobId: '12345',
        applicationData: applicationData,
      );

      if (response.statusCode == 200) {
        print('Application submitted successfully: ${response.data}');
        // Handle successful application
        // Show success message, update UI
      }
    } on DioException catch (e) {
      print('Failed to apply for job: ${e.error}');
      // Handle error
    } catch (e) {
      print('Unexpected error: $e');
    }
  }

  /// Example: Handle logout
  static Future<void> exampleLogout() async {
    try {
      final response = await ApiService.logout();

      if (response.statusCode == 200) {
        print('Logout successful');
        // Handle successful logout
        // Clear local data, navigate to login screen
      }
    } on DioException catch (e) {
      print('Logout failed: ${e.error}');
      // Handle logout error
    } catch (e) {
      print('Unexpected error: $e');
    }
  }

  /// Example: Error handling with user-friendly messages
  static String getErrorMessage(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.connectionError:
        return 'No internet connection. Please check your network.';
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        switch (statusCode) {
          case 400:
            return 'Invalid request. Please check your input.';
          case 401:
            return 'Please login to continue.';
          case 403:
            return 'You don\'t have permission to perform this action.';
          case 404:
            return 'The requested resource was not found.';
          case 422:
            return 'Please check your input and try again.';
          case 500:
            return 'Server error. Please try again later.';
          default:
            return 'An error occurred. Please try again.';
        }
      default:
        return 'An unexpected error occurred.';
    }
  }
}
