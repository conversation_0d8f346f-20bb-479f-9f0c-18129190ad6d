import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'api_service.dart';

/// Example widget showing how to integrate the API service in your Flutter screens
class ApiIntegrationExample extends StatefulWidget {
  const ApiIntegrationExample({super.key});

  @override
  State<ApiIntegrationExample> createState() => _ApiIntegrationExampleState();
}

class _ApiIntegrationExampleState extends State<ApiIntegrationExample> {
  bool _isLoading = false;
  String _message = '';
  List<dynamic> _jobs = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API Integration Example'),
        backgroundColor: const Color(0xFF1F41BB),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Login Example
            ElevatedButton(
              onPressed: _isLoading ? null : _performLogin,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1F41BB),
                foregroundColor: Colors.white,
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('Test Login'),
            ),
            
            const SizedBox(height: 16),
            
            // Get Jobs Example
            ElevatedButton(
              onPressed: _isLoading ? null : _fetchJobs,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('Fetch Jobs'),
            ),
            
            const SizedBox(height: 16),
            
            // Search Jobs Example
            ElevatedButton(
              onPressed: _isLoading ? null : _searchJobs,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('Search Jobs'),
            ),
            
            const SizedBox(height: 16),
            
            // Message Display
            if (_message.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Text(
                  _message,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            
            const SizedBox(height: 16),
            
            // Jobs List
            if (_jobs.isNotEmpty)
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Jobs:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _jobs.length,
                        itemBuilder: (context, index) {
                          final job = _jobs[index];
                          return Card(
                            margin: const EdgeInsets.only(bottom: 8),
                            child: ListTile(
                              title: Text(job['title'] ?? 'No Title'),
                              subtitle: Text(job['company'] ?? 'No Company'),
                              trailing: Text(job['location'] ?? 'No Location'),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Example login function
  Future<void> _performLogin() async {
    setState(() {
      _isLoading = true;
      _message = 'Attempting to login...';
    });

    try {
      final response = await ApiService.login(
        email: '<EMAIL>',
        password: 'password123',
      );

      setState(() {
        _isLoading = false;
        if (response.statusCode == 200) {
          _message = 'Login successful! Token stored automatically.';
        } else {
          _message = 'Login failed with status: ${response.statusCode}';
        }
      });
    } on DioException catch (e) {
      setState(() {
        _isLoading = false;
        _message = 'Login error: ${e.error ?? e.message}';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _message = 'Unexpected error: $e';
      });
    }
  }

  /// Example fetch jobs function
  Future<void> _fetchJobs() async {
    setState(() {
      _isLoading = true;
      _message = 'Fetching jobs...';
      _jobs.clear();
    });

    try {
      final response = await ApiService.getJobs(
        page: 1,
        limit: 10,
      );

      setState(() {
        _isLoading = false;
        if (response.statusCode == 200) {
          _message = 'Jobs fetched successfully!';
          // Assuming the API returns jobs in a 'data' field
          if (response.data['data'] != null) {
            _jobs = response.data['data'];
          } else {
            _jobs = response.data; // If jobs are directly in response
          }
        } else {
          _message = 'Failed to fetch jobs. Status: ${response.statusCode}';
        }
      });
    } on DioException catch (e) {
      setState(() {
        _isLoading = false;
        _message = 'Fetch jobs error: ${e.error ?? e.message}';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _message = 'Unexpected error: $e';
      });
    }
  }

  /// Example search jobs function
  Future<void> _searchJobs() async {
    setState(() {
      _isLoading = true;
      _message = 'Searching for Flutter jobs...';
      _jobs.clear();
    });

    try {
      final response = await ApiService.searchJobs(
        query: 'Flutter Developer',
        page: 1,
        limit: 5,
      );

      setState(() {
        _isLoading = false;
        if (response.statusCode == 200) {
          _message = 'Search completed successfully!';
          // Assuming the API returns jobs in a 'data' field
          if (response.data['data'] != null) {
            _jobs = response.data['data'];
          } else {
            _jobs = response.data; // If jobs are directly in response
          }
        } else {
          _message = 'Search failed. Status: ${response.statusCode}';
        }
      });
    } on DioException catch (e) {
      setState(() {
        _isLoading = false;
        _message = 'Search error: ${e.error ?? e.message}';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _message = 'Unexpected error: $e';
      });
    }
  }
}
