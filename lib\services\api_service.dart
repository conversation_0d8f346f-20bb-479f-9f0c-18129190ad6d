import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../presentation/user_homepage_screen/job_seeker_onboarding.dart';
import 'package:http_parser/http_parser.dart';
import 'package:mime/mime.dart';

import 'api_client.dart';
import 'user_api_endpoints.dart';
import 'session_manager.dart';

class Education {
  final String schoolName;
  final String degree;

  Education({required this.schoolName, required this.degree});

  Map<String, dynamic> toJson() => {
        'school_name': schoolName,
        'degree': degree,
      };
}

class WorkExperience {
  final String jobTitle;
  final String company;
  final String startDate;
  final String endDate;

  WorkExperience({
    required this.jobTitle,
    required this.company,
    required this.startDate,
    required this.endDate,
  });

  Map<String, dynamic> toJson() => {
        'job_title': jobTitle,
        'company': company,
        'start_date': startDate,
        'end_date': endDate,
      };
}

class Skills {
  final String name;
  final int level;
  final String proficiency;

  Skills({
    required this.name,
    required this.level,
    required this.proficiency,
  });

  Map<String, dynamic> toJson() => {
        'name': name,
        'level': level,
        'proficiency': proficiency,
      };
}

/// Service layer for API calls using the ApiClient
class ApiService {
  static final ApiClient _apiClient = ApiClient.instance;

  /// Login user with email and password
  static Future<Response> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _apiClient.post(
        UserApiEndpoints.login,
        data: {
          'email': email,
          'password': password,
        },
      );

      // Extract access token from the response and set auth token
      if (response.statusCode == 200 && response.data['access_token'] != null) {
        final data = response.data;

        // Set access token
        _apiClient.setAuthToken(data['access_token']);

        // Calculate token expiry from expires_in timestamp
        DateTime? tokenExpiry;
        if (data['expires_in'] != null) {
          tokenExpiry =
              DateTime.fromMillisecondsSinceEpoch(data['expires_in'] * 1000);
        } else {
          // Fallback to 24 hours if expires_in not provided
          tokenExpiry = DateTime.now().add(const Duration(hours: 24));
        }

        // Save session using SessionManager
        await SessionManager.instance.saveSession(
          accessToken: data['access_token'],
          refreshToken: data['refresh_token'],
          userId: data['user_id'],
          userType: data['user_type'],
          user: data['user'],
          tokenExpiry: tokenExpiry,
        );

        // Also save to SharedPreferences for backward compatibility
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('access_token', data['access_token']);
        await prefs.setString('refresh_token', data['refresh_token']);
        await prefs.setString('user_id', data['user_id']);
        await prefs.setString('user_type', data['user_type']);

        // Save user object as JSON string
        final userJson = jsonEncode(data['user']);
        await prefs.setString('user', userJson);
      }

      return response;
    } catch (e) {
      rethrow;
    }
  }

  /// Register new user
  // static Future<Response> register({
  //   required String name,
  //   required String email,
  //   required String password,
  //   required String userType,
  // }) async {
  //   try {
  //     return await _apiClient.post(
  //       UserApiEndpoints.register,
  //       data: {
  //         'name': name,
  //         'email': email,
  //         'password': password,
  //         'user_type': userType,
  //       },
  //     );
  //   } catch (e) {
  //     rethrow;
  //   }
  // }
  static Future<Response> register({
    required String name,
    required String email,
    required String password,
    required String userType,
  }) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.register,
        data: {
          'name': name,
          'email': email,
          'password': password,
          'user_type': userType,
        },
      );
    } on DioException catch (e) {
      // Return the error response instead of rethrowing
      if (e.response != null) {
        return e.response!;
      }
      rethrow;
    } catch (e) {
      rethrow;
    }
  }

  /// Google Sign in
  static Future<Response> googleSignIn(
      {required String idToken, String? userType}) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.googleSignIn,
        data: {
          'id_token': idToken,
          'user_type': userType,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Logout user
  static Future<Response> logout() async {
    try {
      final response = await _apiClient.post(UserApiEndpoints.userLogout);

      // Clear stored token and session
      _apiClient.clearAuthToken();
      await SessionManager.instance.clearSession();

      // Also clear SharedPreferences for backward compatibility
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('access_token');
      await prefs.remove('refresh_token');
      await prefs.remove('user_id');
      await prefs.remove('user_type');
      await prefs.remove('user');

      return response;
    } catch (e) {
      rethrow;
    }
  }

  /// Forgot password
  static Future<Response> forgotPassword({required String email}) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.forgotPassword,
        data: {'email': email},
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Reset password without token
  static Future<Response> resetAPassword({
    required String email,
    required String oldPassword,
    required String password,
  }) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.resetPassword,
        data: {
          'email': email,
          'old_password': oldPassword,
          'new_password': password,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  // Reset password with token
  static Future<Response> resetAPasswordWithToken({
    required String userId,
    required String newPassword,
    required String oldPassword,
  }) async {
    try {
      final url = '${UserApiEndpoints.resetPasswordWithToken}/$userId';
      print('API URL: $url'); // Debug line
      print('User ID: $userId'); // Debug line

      return await _apiClient.post(
        url,
        data: {
          'old_password': oldPassword,
          'new_password': newPassword,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Verify OTP
  static Future<Response> verifyOtp({
    required String email,
    required String otp,
  }) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.verifyUser,
        data: {
          'email': email,
          'otp': otp,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Resend OTP
  static Future<Response> resendOtp({required String email}) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.resendOtp,
        data: {'email': email},
      );
    } catch (e) {
      rethrow;
    }
  }

// Update user profile with optional fields and image upload
  static Future<Response> updateUserProfile({
    String? name,
    String? email,
    String? phone,
    String? imagePath, // File path for image upload
    List<String>? favoriteNews,
    List<String>? favoriteEvents,
  }) async {
    try {
      // Build form data map for fields that are provided
      Map<String, dynamic> formDataMap = {};

      // Only add fields that are not null to support partial updates
      if (name != null && name.isNotEmpty) {
        formDataMap['name'] = name;
      }
      if (email != null && email.isNotEmpty) {
        formDataMap['email'] = email;
      }
      if (phone != null && phone.isNotEmpty) {
        formDataMap['phone'] = phone;
      }
      if (favoriteNews != null && favoriteNews.isNotEmpty) {
        formDataMap['favorite_news'] = favoriteNews;
      }
      if (favoriteEvents != null && favoriteEvents.isNotEmpty) {
        formDataMap['favorite_events'] = favoriteEvents;
      }

      // Handle image file upload if provided
      if (imagePath != null && imagePath.isNotEmpty) {
        // Determine content type based on file extension
        String fileName = 'profile_image.jpg';
        MediaType contentType = MediaType('image', 'jpeg');
        String filePath = imagePath.toLowerCase();

        if (filePath.endsWith('.png')) {
          fileName = 'profile_image.png';
          contentType = MediaType('image', 'png');
        } else if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
          fileName = 'profile_image.jpg';
          contentType = MediaType('image', 'jpeg');
        }

        formDataMap['image'] = await MultipartFile.fromFile(
          imagePath,
          filename: fileName,
          contentType: contentType,
        );
      }

      // Only proceed if there are fields to update
      if (formDataMap.isEmpty) {
        throw Exception('No fields provided for update');
      }

      print("formDataMap $formDataMap");
      final formData = FormData.fromMap(formDataMap);

      // Override the global headers for this specific request
      // Remove Content-Type so Dio can set the correct multipart/form-data header
      final options = Options(
        headers: {
          'Accept': 'application/json',
        },
        contentType:
            'multipart/form-data', // or leave this out and let Dio set it
      );

      return await _apiClient.patch(
        UserApiEndpoints.updateUserProfile,
        data: formData,
        options: options,
      );
    } catch (e) {
      print('Error updating user profile: $e');
      rethrow;
    }
  }

// Alternative: Simple profile update without image (for text fields only)
  static Future<Response> updateUserProfileSimple({
    String? name,
    String? email,
    String? phone,
    List<String>? favoriteNews,
    List<String>? favoriteEvents,
  }) async {
    try {
      Map<String, dynamic> updateData = {};

      if (name != null && name.isNotEmpty) {
        updateData['name'] = name;
      }

      if (email != null && email.isNotEmpty) {
        updateData['email'] = email;
      }

      if (phone != null && phone.isNotEmpty) {
        updateData['phone'] = phone;
      }

      if (favoriteNews != null && favoriteNews.isNotEmpty) {
        updateData['favorite_news'] = favoriteNews;
      }

      if (favoriteEvents != null && favoriteEvents.isNotEmpty) {
        updateData['favorite_events'] = favoriteEvents;
      }

      if (updateData.isEmpty) {
        throw Exception('No fields provided for update');
      }

      return await _apiClient.patch(
        UserApiEndpoints.updateUserProfile,
        data: updateData,
      );
    } catch (e) {
      print('Error updating user profile: $e');
      rethrow;
    }
  }

  /// Get all jobs with optional filters
  static Future<Response> getJobs({
    int page = 1,
    int limit = 10,
    String? status,
    String? search,
  }) async {
    try {
      Map<String, dynamic> queryParams = {
        'page': page,
        'limit': limit,
      };

      if (status != null) queryParams['status'] = status;
      if (search != null) queryParams['search'] = search;

      return await _apiClient.get(
        UserApiEndpoints.allJobPosts,
        queryParameters: queryParams,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Apply for a job
  static Future<Response> applyForJob({
    Map<String, dynamic>? applicationData,
  }) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.applyForJob,
        data: applicationData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Get recommended jobs for user

  static Future<Response> getRecommendedJobs({
    required String userId,
    int page = 1,
    int limit = 10,
    String? search,
    String? jobType,
  }) async {
    try {
      return await _apiClient.get(
          '${UserApiEndpoints.getUserJobRecommendationsUrl(userId)}',
          queryParameters: {
            'page': page,
            'limit': limit,
            'search': search,
            'jobType': jobType,
          });
    } catch (e) {
      rethrow;
    }
  }

// Get all job types
  static Future<Response> getJobTypes() async {
    try {
      return await _apiClient.get(UserApiEndpoints.jobTypes);
    } catch (e) {
      rethrow;
    }
  }

  // Upload profile image
  static Future<Response> uploadProfileImage(String filePath) async {
    try {
      final mimeType = lookupMimeType(filePath) ?? 'image/jpeg';
      FormData formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(
          filePath,
          contentType:
              MediaType.parse(mimeType), // ✅ Set the correct content-type
        ),
      });
      return await _apiClient.post(
        UserApiEndpoints.uploadProfileImage,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Get notifications
  static Future<Response> getNotifications({
    int page = 1,
    int limit = 10,
    String? search,
    String? type,
    String? read,
    String? userId,
  }) async {
    try {
      FormData formData = FormData.fromMap({
        'user_id': userId,
      });
      return await _apiClient.post(
        UserApiEndpoints.getUserNotificationsUrl(),
        data: formData,
        queryParameters: {
          'page': page,
          'limit': limit,
          'search': search,
          'type': type,
          'read': read,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Mark notification as read
  static Future<Response> markNotificationAsRead(
      String notificationId, String userId) async {
    try {
      return await _apiClient.patch(
        '${UserApiEndpoints.markNotificationAsRead}/$notificationId/$userId',
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Get user favorites
  static Future<Response> getUserFavoriteNews({
    String? user_id,
    String? category_id,
    int page = 1,
    int limit = 10,
    String? search,
  }) async {
    try {
      return await _apiClient.get(
          '${UserApiEndpoints.getUserFavoriteNews}/$user_id',
          queryParameters: {
            'page': page,
            'limit': limit,
            'category_id': category_id,
            'search': search,
          });
    } catch (e) {
      rethrow;
    }
  }

  /// Add news to user favorites
  static Future<Response> addNewsToUserFavorites(
      String newsId, String userId) async {
    try {
      // Create FormData object
      FormData formData = FormData.fromMap({
        'news_id': newsId,
        'user_id': userId,
      });

      return await _apiClient.post(
        UserApiEndpoints.addNewsToFavorites,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Remove news from user favorites
  static Future<Response> removeNewsFromUserFavorites(
      String newsId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'news_id': newsId,
        'user_id': userId,
      });
      return await _apiClient.delete(
        UserApiEndpoints.removeNewsFromFavorites,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Get user fav events
  static Future<Response> getUserFavoriteEvents({
    String? user_id,
    String? category_id,
    int page = 1,
    int limit = 10,
    String? search,
    String? status,
    String? sort,
  }) async {
    try {
      return await _apiClient.get(
          '${UserApiEndpoints.getUserFavoriteEvents}/$user_id',
          queryParameters: {
            'page': page,
            'limit': limit,
            'category_id': category_id,
            'search': search,
            'status': status,
            'sort': sort,
          });
    } catch (e) {
      rethrow;
    }
  }

  // Add event to user favorites
  static Future<Response> addEventToUserFavorites(
      String eventId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'event_id': eventId,
        'user_id': userId,
      });
      return await _apiClient.post(
        UserApiEndpoints.addEventToFavorites,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Remove event from user favorites
  static Future<Response> removeEventFromUserFavorites(
      String eventId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'event_id': eventId,
        'user_id': userId,
      });
      return await _apiClient.delete(
        UserApiEndpoints.removeEventFromFavorites,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Check if user liked event
  static Future<Response> checkIfUserLikedEvent(
      String eventId, String userId) async {
    try {
      final url =
          '${UserApiEndpoints.checkIfUserLikedEvent}?event_id=$eventId&user_id=$userId';
      return await _apiClient.post(url);
    } catch (e) {
      rethrow;
    }
  }

  // user to like event
  static Future<Response> userToLikeEvent(String eventId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'event_id': eventId,
        'user_id': userId,
      });
      return _apiClient.post(
        UserApiEndpoints.userToLikeEvent,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // user to unlike event
  static Future<Response> userToUnLikeEvent(
      String eventId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'event_id': eventId,
        'user_id': userId,
      });
      return _apiClient.post(
        UserApiEndpoints.userToUnlikeEvent,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // check if user favorited event
  static Future<Response> checkIfUserFavoritedEvent(
      String eventId, String userId) async {
    try {
      return await _apiClient.get(
        '${UserApiEndpoints.checkIfUserFavoritedEvent}/$userId/$eventId',
      );
    } catch (e) {
      rethrow;
    }
  }

  // check if user favorited news
  static Future<Response> checkIfUserFavoritedNews(
      String newsId, String userId) async {
    try {
      return await _apiClient.get(
        '${UserApiEndpoints.checkIfUserFavoritedNews}/$userId/$newsId',
      );
    } catch (e) {
      rethrow;
    }
  }

  // user to send event opinion
  static Future<Response> userToSendEventOpinion(
      String eventId, String userId, String content) async {
    try {
      FormData formData = FormData.fromMap({
        'event_id': eventId,
        'user_id': userId,
        'content': content,
      });
      return _apiClient.post(
        UserApiEndpoints.sendEventOpinion,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // user to share event
  static Future<Response> userToShareEvent(
      String eventId, String userId, String platform) async {
    try {
      FormData formData = FormData.fromMap({
        'event_id': eventId,
        'user_id': userId,
        'platform': platform,
      });
      return _apiClient.post(
        UserApiEndpoints.userToShareEvent,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Get user fav job posts
  static Future<Response> getUserFavoriteJobPost({
    String? user_id,
    int page = 1,
    int limit = 10,
    String? jobType,
    String? search,
  }) async {
    try {
      return await _apiClient.get(
          '${UserApiEndpoints.getUserFavoriteJobPosts}/$user_id',
          queryParameters: {
            'page': page,
            'limit': limit,
            'jobType': jobType,
            'search': search,
          });
    } catch (e) {
      rethrow;
    }
  }

  // Add job post to user favorites
  static Future<Response> addJobPostToUserFavorites(
      String jobId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'job_post_id': jobId,
        'user_id': userId,
      });
      return await _apiClient.post(
        UserApiEndpoints.addFavoriteJobPost,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Remove job post from user favorites
  static Future<Response> removeJobPostFromUserFavorites(
      String jobId, String userId) async {
    try {
      FormData formData = FormData.fromMap({
        'job_post_id': jobId,
        'user_id': userId,
      });
      return await _apiClient.delete(
        UserApiEndpoints.removeFavoriteJobPost,
        data: formData,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Get Popular Events
  static Future<Response> getPopularEvents() async {
    try {
      return await _apiClient.get(UserApiEndpoints.popularEvents);
    } catch (e) {
      rethrow;
    }
  }

  // Get Events
  static Future<Response> getEvents({
    int page = 1,
    int limit = 10,
    String? search,
    String? category_id,
  }) async {
    try {
      return await _apiClient.get(UserApiEndpoints.allEvents, queryParameters: {
        'page': page,
        'limit': limit,
        'search': search,
        'category_id': category_id,
      });
    } catch (e) {
      rethrow;
    }
  }

  // Get Event by Id
  static Future<Response> getEventById(String eventId) async {
    try {
      return await _apiClient.get('${UserApiEndpoints.eventById}/$eventId');
    } catch (e) {
      rethrow;
    }
  }

  // Get Event Category by Id
  static Future<Response> getEventCategoryById(String categoryId) async {
    try {
      return await _apiClient
          .get('${UserApiEndpoints.eventCategoryById}/$categoryId');
    } catch (e) {
      rethrow;
    }
  }

  // Get All Event Categories
  static Future<Response> getEventCategories() async {
    try {
      return await _apiClient.get(UserApiEndpoints.eventCategory);
    } catch (e) {
      rethrow;
    }
  }

  //Get Blogs
  static Future<Response> getBlogs({
    int page = 1,
    int limit = 10,
    String? search,
    String? category_id,
  }) async {
    try {
      return await _apiClient.get(UserApiEndpoints.allBlogs, queryParameters: {
        'page': page,
        'limit': limit,
        'search': search,
        'category_id': category_id,
      });
    } catch (e) {
      rethrow;
    }
  }

  // Get Blog by Id
  static Future<Response> getBlogById(String blogId) async {
    try {
      return await _apiClient.get('${UserApiEndpoints.blogById}/$blogId');
    } catch (e) {
      rethrow;
    }
  }

  // Get Blog Category by Id
  static Future<Response> getBlogCategoryById(String categoryId) async {
    try {
      return await _apiClient
          .get('${UserApiEndpoints.blogCategoryById}/$categoryId');
    } catch (e) {
      rethrow;
    }
  }

  // Get All Blog Categories
  static Future<Response> getBlogCategories() async {
    try {
      return await _apiClient.get(UserApiEndpoints.blogCategory);
    } catch (e) {
      rethrow;
    }
  }

  //Get News
  static Future<Response> getNews({
    int page = 1,
    int limit = 10,
    String? search,
    String? category_id,
  }) async {
    try {
      return await _apiClient.get(UserApiEndpoints.allNews, queryParameters: {
        'page': page,
        'limit': limit,
        'search': search,
        'category_id': category_id,
      });
    } catch (e) {
      rethrow;
    }
  }

  // Get News by Id
  static Future<Response> getNewsById(String newsId) async {
    try {
      return await _apiClient.get('${UserApiEndpoints.newsById}/$newsId');
    } catch (e) {
      rethrow;
    }
  }

  // Get News Category by Id
  static Future<Response> getNewsCategoryById(String categoryId) async {
    try {
      return await _apiClient
          .get('${UserApiEndpoints.newsCategoryById}/$categoryId');
    } catch (e) {
      rethrow;
    }
  }

  // Get All News Categories
  static Future<Response> getNewsCategories() async {
    try {
      return await _apiClient.get(UserApiEndpoints.newsCategory);
    } catch (e) {
      rethrow;
    }
  }

  //Get jOBpOST
  static Future<Response> getJobPost({
    int page = 1,
    int limit = 10,
    String? search,
  }) async {
    try {
      return await _apiClient
          .get(UserApiEndpoints.allJobPosts, queryParameters: {
        'page': page,
        'limit': limit,
        'search': search,
      });
    } catch (e) {
      rethrow;
    }
  }

  // Get Job Post by Id
  static Future<Response> getJobPostById(String jobId) async {
    try {
      return await _apiClient.get('${UserApiEndpoints.jobPostById}/$jobId');
    } catch (e) {
      rethrow;
    }
  }




  // Create job seeker profile

static Future<Response> createJobSeekerProfile(
    JobSeekerProfile profile, String userId) async {
  try {
    // Build form data map
    Map<String, dynamic> formDataMap = {
      'profession': profile.profession,
      'years_experience': profile.yearsExperience.toString(),
      'about_me': profile.aboutMe,
      'portfolio_url': profile.portfolioURL,
      'linkedin_url': profile.linkedinURL,
      'location': profile.location,
    };

    // Serialize education
    if (profile.education != null && profile.education.isNotEmpty) {
      formDataMap['education'] =
          jsonEncode(profile.education.map((e) => e.toJson()).toList());
    }

    // Serialize work history
    if (profile.workHistory != null && profile.workHistory.isNotEmpty) {
      formDataMap['work_history'] =
          jsonEncode(profile.workHistory.map((w) => w.toJson()).toList());
    }

    // Serialize skills
    if (profile.skills != null && profile.skills.isNotEmpty) {
      formDataMap['skills'] =
          jsonEncode(profile.skills.map((s) => s.toJson()).toList());
    }

    // Serialize interests
    if (profile.interests != null && profile.interests.isNotEmpty) {
      formDataMap['interests'] =
          jsonEncode(profile.interests.map((i) => i.toJson()).toList());
    }

    // Handle image file upload
    if (profile.image != null && profile.image!.isNotEmpty) {
      formDataMap['image'] = await MultipartFile.fromFile(
        profile.image,
        filename: 'profile_image.jpg',
        contentType: MediaType('image', 'jpeg'),
      );
    }

    // Handle resume file upload
    if (profile.resumeURL != null && profile.resumeURL.isNotEmpty) {
      String fileName = 'resume.pdf';
      MediaType contentType = MediaType('application', 'pdf');

      String filePath = profile.resumeURL.toLowerCase();
      if (filePath.endsWith('.doc')) {
        fileName = 'resume.doc';
        contentType = MediaType('application', 'msword');
      } else if (filePath.endsWith('.docx')) {
        fileName = 'resume.docx';
        contentType = MediaType(
          'application',
          'vnd.openxmlformats-officedocument.wordprocessingml.document',
        );
      }

      formDataMap['resume'] = await MultipartFile.fromFile(
        profile.resumeURL,
        filename: fileName,
        contentType: contentType,
      );
    }

    final formData = FormData.fromMap(formDataMap);

    final options = Options(
      headers: {'Accept': 'application/json'},
      contentType: 'multipart/form-data',
    );

    return await _apiClient.post(
      '${UserApiEndpoints.jobSeekerOnboarding}/$userId',
      data: formData,
      options: options,
    );
  } catch (e) {
    print('Error creating job seeker profile: $e');
    rethrow;
  }
}


  // Get job seeker profile
  static Future<Response> getJobSeekerProfile(String userId) async {
    try {
      return await _apiClient.get(
        '${UserApiEndpoints.getJobSeekerOnboarding}/$userId',
      );
    } catch (e) {
      rethrow;
    }
  }

// Update(Patch) job seeker profile
  static Future<Response> updateJobSeekerProfile(
      JobSeekerProfile profile, String userId) async {
    try {
      // Build form data map
      Map<String, dynamic> formDataMap = {};

      // Only add fields that are not null/empty to support partial updates
      if (profile.profession != null && profile.profession.isNotEmpty) {
        formDataMap['profession'] = profile.profession;
      }

      if (profile.yearsExperience != null) {
        formDataMap['years_experience'] = profile.yearsExperience.toString();
      }

      if (profile.aboutMe != null && profile.aboutMe.isNotEmpty) {
        formDataMap['about_me'] = profile.aboutMe;
      }

      if (profile.portfolioURL != null && profile.portfolioURL.isNotEmpty) {
        formDataMap['portfolio_url'] = profile.portfolioURL;
      }

      if (profile.linkedinURL != null && profile.linkedinURL.isNotEmpty) {
        formDataMap['linkedin_url'] = profile.linkedinURL;
      }

      if (profile.location != null && profile.location.isNotEmpty) {
        formDataMap['location'] = profile.location;
      }

      // Handle complex objects - serialize to JSON strings
      if (profile.education != null && profile.education.isNotEmpty) {
        formDataMap['education'] = profile.education.toString();
      }

      if (profile.workHistory != null && profile.workHistory.isNotEmpty) {
        formDataMap['work_history'] = profile.workHistory.toString();
      }

      if (profile.skills != null && profile.skills.isNotEmpty) {
        formDataMap['skills'] = profile.skills.toString();
      }

      // Handle interests array as JSON string
      if (profile.interests != null && profile.interests.isNotEmpty) {
        formDataMap['interests'] = profile.interests.toString();
      }

      // Handle image file upload
      if (profile.image != null && profile.image!.isNotEmpty) {
        formDataMap['image'] = await MultipartFile.fromFile(
          profile.image,
          filename: 'profile_image.jpg',
          contentType: MediaType('image', 'jpeg'),
        );
      }

      // Handle resume file upload (note: using 'resume' not 'resume_url')
      if (profile.resumeURL != null && profile.resumeURL.isNotEmpty) {
        // Determine content type based on file extension
        String fileName = 'resume.pdf';
        MediaType contentType = MediaType('application', 'pdf');
        String filePath = profile.resumeURL.toLowerCase();

        if (filePath.endsWith('.doc')) {
          fileName = 'resume.doc';
          contentType = MediaType('application', 'msword');
        } else if (filePath.endsWith('.docx')) {
          fileName = 'resume.docx';
          contentType = MediaType('application',
              'vnd.openxmlformats-officedocument.wordprocessingml.document');
        }

        formDataMap['resume'] = await MultipartFile.fromFile(
          profile.resumeURL,
          filename: fileName,
          contentType: contentType,
        );
      }

      final formData = FormData.fromMap(formDataMap);

      return await _apiClient.patch(
        '${UserApiEndpoints.jobSeekerOnboarding}/$userId',
        data: formData,
        options: Options(
          sendTimeout: const Duration(minutes: 2),
          receiveTimeout: const Duration(minutes: 2),
        ),
      );
    } catch (e) {
      print('Error updating job seeker profile: $e');
      rethrow;
    }
  }

  /// Refresh access token using refresh token
  static Future<Response> refreshToken({required String refreshToken}) async {
    try {
      return await _apiClient.post(
        UserApiEndpoints.refreshToken,
        options: Options(
          headers: {
            'Authorization': 'Bearer $refreshToken',
          },
        ),
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Initialize API client with stored session
  static Future<void> initializeSession() async {
    await _apiClient.initializeWithStoredSession();
  }
}
