import 'package:flutter/foundation.dart';
import 'session_manager.dart';
import 'api_service.dart';

/// Service for handling app initialization and session validation
class AppInitializationService {
  static AppInitializationService? _instance;
  
  // Private constructor
  AppInitializationService._internal();
  
  // Singleton instance getter
  static AppInitializationService get instance {
    _instance ??= AppInitializationService._internal();
    return _instance!;
  }

  /// Initialize the app and determine the initial route
  Future<AppInitializationResult> initializeApp() async {
    try {
      if (kDebugMode) {
        print('🚀 Starting app initialization...');
      }

      // Initialize API client with stored session
      await ApiService.initializeSession();
      
      // Check for valid session
      final sessionManager = SessionManager.instance;
      final hasValidSession = await sessionManager.hasValidSession();
      
      if (kDebugMode) {
        final sessionSummary = await sessionManager.getSessionSummary();
        print('📊 Session Summary: $sessionSummary');
      }
      
      if (hasValidSession) {
        // Get user data to determine navigation
        final userType = await sessionManager.getUserType();
        final user = await sessionManager.getUser();
        
        if (userType != null && user != null) {
          if (kDebugMode) {
            print('✅ Valid session found for user type: $userType');
          }
          
          return AppInitializationResult(
            shouldNavigateToHome: true,
            userType: userType,
            user: user,
          );
        }
      }
      
      if (kDebugMode) {
        print('❌ No valid session found, navigating to onboarding');
      }
      
      return AppInitializationResult(
        shouldNavigateToHome: false,
      );
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error during app initialization: $e');
      }
      
      // Clear potentially corrupted session data
      try {
        await SessionManager.instance.clearSession();
      } catch (clearError) {
        if (kDebugMode) {
          print('❌ Error clearing session: $clearError');
        }
      }
      
      return AppInitializationResult(
        shouldNavigateToHome: false,
        error: e.toString(),
      );
    }
  }

  /// Validate current session and refresh if needed
  Future<bool> validateAndRefreshSession() async {
    try {
      final sessionManager = SessionManager.instance;
      
      // Check if we have tokens
      final accessToken = await sessionManager.getAccessToken();
      final refreshToken = await sessionManager.getRefreshToken();
      
      if (accessToken == null || refreshToken == null) {
        if (kDebugMode) {
          print('❌ No tokens available');
        }
        return false;
      }
      
      // Check if access token is expired
      final isExpired = await sessionManager.isAccessTokenExpired();
      
      if (!isExpired) {
        if (kDebugMode) {
          print('✅ Access token is still valid');
        }
        return true;
      }
      
      if (kDebugMode) {
        print('🔄 Access token expired, attempting refresh...');
      }
      
      // Try to refresh the token
      try {
        final response = await ApiService.refreshToken(refreshToken: refreshToken);
        
        if (response.statusCode == 200 && response.data['access_token'] != null) {
          // Update session with new tokens
          await sessionManager.updateAccessToken(
            response.data['access_token'],
            newExpiry: DateTime.now().add(const Duration(hours: 24)),
          );
          
          if (kDebugMode) {
            print('✅ Token refreshed successfully');
          }
          return true;
        }
      } catch (refreshError) {
        if (kDebugMode) {
          print('❌ Token refresh failed: $refreshError');
        }
      }
      
      // Refresh failed, clear session
      await sessionManager.clearSession();
      return false;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error validating session: $e');
      }
      return false;
    }
  }
}

/// Result of app initialization
class AppInitializationResult {
  final bool shouldNavigateToHome;
  final String? userType;
  final Map<String, dynamic>? user;
  final String? error;
  
  AppInitializationResult({
    required this.shouldNavigateToHome,
    this.userType,
    this.user,
    this.error,
  });
  
  bool get hasError => error != null;
  bool get isJobSeeker => userType == 'job_seeker';
  bool get isRecruiter => userType == 'recruiter';
}
