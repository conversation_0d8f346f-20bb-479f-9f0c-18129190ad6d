import 'package:json_annotation/json_annotation.dart';

part 'education.g.dart';

@JsonSerializable()
class Education {
  @JsonKey(name: 'school_name')
  final String schoolName;

  final String degree;
  final String year;

  Education({
    required this.schoolName,
    required this.degree,
    required this.year,
  });

  factory Education.fromJson(Map<String, dynamic> json) =>
      _$EducationFromJson(json);
  Map<String, dynamic> toJson() => _$EducationToJson(this);
}
