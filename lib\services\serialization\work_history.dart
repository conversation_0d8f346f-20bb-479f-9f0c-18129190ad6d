import 'package:json_annotation/json_annotation.dart';

part 'work_history.g.dart';

@JsonSerializable()
class WorkHistory {
  final String company;
  final String role;
  final String duration;

  WorkHistory({
    required this.company,
    required this.role,
    required this.duration,
  });

  factory WorkHistory.fromJson(Map<String, dynamic> json) =>
      _$WorkHistoryFromJson(json);
  Map<String, dynamic> toJson() => _$WorkHistoryToJson(this);
}
