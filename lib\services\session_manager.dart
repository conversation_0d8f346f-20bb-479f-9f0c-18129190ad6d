import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

/// Session management service for handling authentication tokens and user data
class SessionManager {
  static SessionManager? _instance;
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userIdKey = 'user_id';
  static const String _userTypeKey = 'user_type';
  static const String _userKey = 'user';
  static const String _tokenExpiryKey = 'token_expiry';

  // Private constructor
  SessionManager._internal();

  // Singleton instance getter
  static SessionManager get instance {
    _instance ??= SessionManager._internal();
    return _instance!;
  }

  /// Save session data after successful login
  Future<void> saveSession({
    required String accessToken,
    required String refreshToken,
    required String userId,
    required String userType,
    required Map<String, dynamic> user,
    DateTime? tokenExpiry,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setString(_accessTokenKey, accessToken);
      await prefs.setString(_refreshTokenKey, refreshToken);
      await prefs.setString(_userIdKey, userId);
      await prefs.setString(_userTypeKey, userType);
      await prefs.setString(_userKey, jsonEncode(user));

      // Set token expiry (default to 24 hours if not provided)
      final expiry =
          tokenExpiry ?? DateTime.now().add(const Duration(hours: 24));
      await prefs.setString(_tokenExpiryKey, expiry.toIso8601String());

      if (kDebugMode) {
        print('✅ Session saved successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving session: $e');
      }
      rethrow;
    }
  }

  /// Get stored access token
  Future<String?> getAccessToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_accessTokenKey);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting access token: $e');
      }
      return null;
    }
  }

  /// Get stored refresh token
  Future<String?> getRefreshToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_refreshTokenKey);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting refresh token: $e');
      }
      return null;
    }
  }

  /// Get stored user ID
  Future<String?> getUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_userIdKey);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting user ID: $e');
      }
      return null;
    }
  }

  /// Get stored user type
  Future<String?> getUserType() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_userTypeKey);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting user type: $e');
      }
      return null;
    }
  }

  /// Get stored user data
  Future<Map<String, dynamic>?> getUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userString = prefs.getString(_userKey);
      if (userString != null) {
        return jsonDecode(userString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting user data: $e');
      }
      return null;
    }
  }

  /// Check if access token is expired
  Future<bool> isAccessTokenExpired() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final expiryString = prefs.getString(_tokenExpiryKey);

      if (expiryString == null) {
        if (kDebugMode) {
          print('🕐 No token expiry found - considering expired');
        }
        return true; // Consider expired if no expiry date
      }

      final expiry = DateTime.parse(expiryString);
      final now = DateTime.now();

      // Consider token expired if it expires within the next 5 minutes (buffer for network calls)
      final bufferTime = const Duration(minutes: 5);
      final isExpired = now.isAfter(expiry.subtract(bufferTime));

      if (kDebugMode) {
        print('🕐 Token expiry check: ${isExpired ? 'EXPIRED' : 'VALID'}');
        print('   Current time: $now');
        print('   Token expiry: $expiry');
        print(
            '   Time until expiry: ${expiry.difference(now).inMinutes} minutes');
      }

      return isExpired;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking token expiry: $e');
      }
      return true; // Consider expired on error
    }
  }

  /// Check if user has a valid session
  Future<bool> hasValidSession() async {
    try {
      final accessToken = await getAccessToken();
      final refreshToken = await getRefreshToken();

      if (accessToken == null || refreshToken == null) {
        if (kDebugMode) {
          print('❌ No tokens found');
        }
        return false;
      }

      // If access token is expired, check if refresh token exists
      final isExpired = await isAccessTokenExpired();
      if (isExpired) {
        if (kDebugMode) {
          print('⚠️ Access token expired, but refresh token available');
        }
        // We have a refresh token, so session can be refreshed
        return true;
      }

      if (kDebugMode) {
        print('✅ Valid session found');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking session validity: $e');
      }
      return false;
    }
  }

  /// Update access token after refresh
  Future<void> updateAccessToken(String newAccessToken,
      {DateTime? newExpiry}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_accessTokenKey, newAccessToken);

      if (newExpiry != null) {
        await prefs.setString(_tokenExpiryKey, newExpiry.toIso8601String());
      }

      if (kDebugMode) {
        print('✅ Access token updated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating access token: $e');
      }
      rethrow;
    }
  }

  /// Clear all session data (logout)
  Future<void> clearSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.remove(_accessTokenKey);
      await prefs.remove(_refreshTokenKey);
      await prefs.remove(_userIdKey);
      await prefs.remove(_userTypeKey);
      await prefs.remove(_userKey);
      await prefs.remove(_tokenExpiryKey);

      if (kDebugMode) {
        print('✅ Session cleared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing session: $e');
      }
      rethrow;
    }
  }

  /// Get session summary for debugging
  Future<Map<String, dynamic>> getSessionSummary() async {
    return {
      'hasAccessToken': await getAccessToken() != null,
      'hasRefreshToken': await getRefreshToken() != null,
      'userId': await getUserId(),
      'userType': await getUserType(),
      'isExpired': await isAccessTokenExpired(),
      'hasValidSession': await hasValidSession(),
    };
  }
}
