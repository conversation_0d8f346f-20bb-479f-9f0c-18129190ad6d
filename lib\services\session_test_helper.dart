import 'package:flutter/foundation.dart';
import 'session_manager.dart';
import 'app_initialization_service.dart';

/// Helper class for testing session management functionality
class SessionTestHelper {
  static SessionTestHelper? _instance;

  SessionTestHelper._internal();

  static SessionTestHelper get instance {
    _instance ??= SessionTestHelper._internal();
    return _instance!;
  }

  /// Test session management functionality
  Future<void> runSessionTests() async {
    if (kDebugMode) {
      print('🧪 Starting Session Management Tests...\n');

      await _testSessionSaving();
      await _testSessionRetrieval();
      await _testSessionValidation();
      await _testTokenExpiry();
      await _testRefreshTokenResponse();
      await _testSessionClearing();
      await _testAppInitialization();

      print('✅ All Session Management Tests Completed!\n');
    }
  }

  /// Test saving session data
  Future<void> _testSessionSaving() async {
    if (kDebugMode) {
      print('🔍 Testing Session Saving...');
    }

    try {
      final sessionManager = SessionManager.instance;

      // Test data
      final testUser = {
        'id': 'test_user_123',
        'name': 'Test User',
        'email': '<EMAIL>',
      };

      await sessionManager.saveSession(
        accessToken: 'test_access_token_123',
        refreshToken: 'test_refresh_token_456',
        userId: 'test_user_123',
        userType: 'job_seeker',
        user: testUser,
      );

      if (kDebugMode) {
        print('✅ Session saved successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Session saving failed: $e');
      }
    }
  }

  /// Test retrieving session data
  Future<void> _testSessionRetrieval() async {
    if (kDebugMode) {
      print('🔍 Testing Session Retrieval...');
    }

    try {
      final sessionManager = SessionManager.instance;

      final accessToken = await sessionManager.getAccessToken();
      final refreshToken = await sessionManager.getRefreshToken();
      final userId = await sessionManager.getUserId();
      final userType = await sessionManager.getUserType();
      final user = await sessionManager.getUser();

      if (kDebugMode) {
        print('📊 Retrieved Session Data:');
        print('   Access Token: ${accessToken?.substring(0, 10)}...');
        print('   Refresh Token: ${refreshToken?.substring(0, 10)}...');
        print('   User ID: $userId');
        print('   User Type: $userType');
        print('   User: $user');
      }

      // Verify data
      assert(accessToken == 'test_access_token_123');
      assert(refreshToken == 'test_refresh_token_456');
      assert(userId == 'test_user_123');
      assert(userType == 'job_seeker');
      assert(user != null && user['id'] == 'test_user_123');

      if (kDebugMode) {
        print('✅ Session retrieval successful');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Session retrieval failed: $e');
      }
    }
  }

  /// Test session validation
  Future<void> _testSessionValidation() async {
    if (kDebugMode) {
      print('🔍 Testing Session Validation...');
    }

    try {
      final sessionManager = SessionManager.instance;

      final hasValidSession = await sessionManager.hasValidSession();
      final isExpired = await sessionManager.isAccessTokenExpired();
      final summary = await sessionManager.getSessionSummary();

      if (kDebugMode) {
        print('📊 Session Validation Results:');
        print('   Has Valid Session: $hasValidSession');
        print('   Is Token Expired: $isExpired');
        print('   Session Summary: $summary');
      }

      if (kDebugMode) {
        print('✅ Session validation completed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Session validation failed: $e');
      }
    }
  }

  /// Test token expiry handling
  Future<void> _testTokenExpiry() async {
    if (kDebugMode) {
      print('🔍 Testing Token Expiry...');
    }

    try {
      final sessionManager = SessionManager.instance;

      // Save session with expired token
      await sessionManager.saveSession(
        accessToken: 'expired_token',
        refreshToken: 'test_refresh_token_456',
        userId: 'test_user_123',
        userType: 'job_seeker',
        user: {'id': 'test_user_123'},
        tokenExpiry:
            DateTime.now().subtract(const Duration(hours: 1)), // Expired
      );

      final isExpired = await sessionManager.isAccessTokenExpired();
      final hasValidSession = await sessionManager.hasValidSession();

      if (kDebugMode) {
        print('📊 Token Expiry Test Results:');
        print('   Is Token Expired: $isExpired');
        print('   Has Valid Session: $hasValidSession');
      }

      // Should be expired but session should still be valid (due to refresh token)
      assert(isExpired == true);
      assert(hasValidSession == true);

      if (kDebugMode) {
        print('✅ Token expiry test passed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Token expiry test failed: $e');
      }
    }
  }

  /// Test refresh token response handling
  Future<void> _testRefreshTokenResponse() async {
    if (kDebugMode) {
      print('🔍 Testing Refresh Token Response Handling...');
      print(
          'ℹ️ Note: This tests the response structure, not the actual API call');
    }

    try {
      final sessionManager = SessionManager.instance;

      // Simulate the actual refresh token response structure
      // The actual API call sends refresh token in Authorization header
      final mockRefreshResponse = {
        "access_token":
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3NfdXVpZCI6IjY4Mzg4MjQ2ODlmYWEyMzgzZjBkOTU4ZSIsImV4cCI6MTc0ODYyMDIzMCwidXNlcl9pZCI6IjY4MzU4YjRiYzc0Nzc0YWU4MjFhY2Y3ZSIsInVzZXJfdHlwZSI6ImpvYl9zZWVrZXIifQ.J8Ar_u0EPzQWLNCRrHEPkBjxJJg8S6V0jYfW6KLwpwo",
        "expires_in": 1748620230,
        "message": "Token refreshed successfully",
        "refresh_token":
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NDkxMzg2MzAsInJlZnJlc2hfdXVpZCI6IjY4Mzg4MjQ2ODlmYWEyMzgzZjBkOTU4ZiIsInVzZXJfaWQiOiI2ODM1OGI0YmM3NDc3NGFlODIxYWNmN2UiLCJ1c2VyX3R5cGUiOiJqb2Jfc2Vla2VyIn0.w11UCWKE5V8x0zm6xoZUsnRX5w4ank4_nkGzL0gRQXU",
        "status": "success",
        "user": {
          "id": "68358b4bc74774ae821acf7e",
          "name": "Ian Tubers",
          "email": "<EMAIL>",
          "phone": "",
          "user_type": "job_seeker",
          "verified": true,
          "created_at": "2025-05-27T09:52:11.499Z",
          "updated_at": "2025-05-27T09:52:11.499Z",
          "favorite_news": [],
          "favorite_events": [],
          "image": "",
          "job_seeker_profile": {
            "profession": "",
            "years_experience": 0,
            "skills": null,
            "about_me": "",
            "interests": null,
            "resume_url": "",
            "portfolio_url": "",
            "linkedin_url": "",
            "education": null,
            "work_history": null,
            "location": "",
            "onboarding_complete": false,
            "favorite_job_posts": [],
            "image": ""
          }
        },
        "user_id": "68358b4bc74774ae821acf7e",
        "user_type": "job_seeker"
      };

      // Test token expiry calculation
      final expiresIn = mockRefreshResponse['expires_in'] as int;
      final tokenExpiry = DateTime.fromMillisecondsSinceEpoch(expiresIn * 1000);

      // Save session with the mock response data
      await sessionManager.saveSession(
        accessToken: mockRefreshResponse['access_token'] as String,
        refreshToken: mockRefreshResponse['refresh_token'] as String,
        userId: mockRefreshResponse['user_id'] as String,
        userType: mockRefreshResponse['user_type'] as String,
        user: mockRefreshResponse['user'] as Map<String, dynamic>,
        tokenExpiry: tokenExpiry,
      );

      // Verify the data was saved correctly
      final savedAccessToken = await sessionManager.getAccessToken();
      final savedRefreshToken = await sessionManager.getRefreshToken();
      final savedUserId = await sessionManager.getUserId();
      final savedUserType = await sessionManager.getUserType();
      final savedUser = await sessionManager.getUser();
      final isExpired = await sessionManager.isAccessTokenExpired();

      if (kDebugMode) {
        print('📊 Refresh Token Response Test Results:');
        print('   Access Token Saved: ${savedAccessToken != null}');
        print('   Refresh Token Saved: ${savedRefreshToken != null}');
        print('   User ID: $savedUserId');
        print('   User Type: $savedUserType');
        print('   User Name: ${savedUser?['name']}');
        print('   Token Expiry: $tokenExpiry');
        print('   Is Token Expired: $isExpired');
      }

      // Verify all data matches
      assert(savedAccessToken == mockRefreshResponse['access_token']);
      assert(savedRefreshToken == mockRefreshResponse['refresh_token']);
      assert(savedUserId == mockRefreshResponse['user_id']);
      assert(savedUserType == mockRefreshResponse['user_type']);
      assert(savedUser?['id'] == mockRefreshResponse['user_id']);

      if (kDebugMode) {
        print('✅ Refresh token response test passed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Refresh token response test failed: $e');
      }
    }
  }

  /// Test session clearing
  Future<void> _testSessionClearing() async {
    if (kDebugMode) {
      print('🔍 Testing Session Clearing...');
    }

    try {
      final sessionManager = SessionManager.instance;

      // Clear session
      await sessionManager.clearSession();

      // Verify session is cleared
      final accessToken = await sessionManager.getAccessToken();
      final refreshToken = await sessionManager.getRefreshToken();
      final hasValidSession = await sessionManager.hasValidSession();

      if (kDebugMode) {
        print('📊 Session Clearing Results:');
        print('   Access Token: $accessToken');
        print('   Refresh Token: $refreshToken');
        print('   Has Valid Session: $hasValidSession');
      }

      // Should all be null/false
      assert(accessToken == null);
      assert(refreshToken == null);
      assert(hasValidSession == false);

      if (kDebugMode) {
        print('✅ Session clearing test passed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Session clearing test failed: $e');
      }
    }
  }

  /// Test app initialization
  Future<void> _testAppInitialization() async {
    if (kDebugMode) {
      print('🔍 Testing App Initialization...');
    }

    try {
      final initService = AppInitializationService.instance;

      // Test with no session
      var result = await initService.initializeApp();

      if (kDebugMode) {
        print('📊 App Initialization (No Session):');
        print('   Should Navigate to Home: ${result.shouldNavigateToHome}');
        print('   User Type: ${result.userType}');
        print('   Has Error: ${result.hasError}');
      }

      assert(result.shouldNavigateToHome == false);

      // Test with valid session
      final sessionManager = SessionManager.instance;
      await sessionManager.saveSession(
        accessToken: 'valid_token',
        refreshToken: 'valid_refresh_token',
        userId: 'test_user_123',
        userType: 'job_seeker',
        user: {'id': 'test_user_123', 'name': 'Test User'},
      );

      result = await initService.initializeApp();

      if (kDebugMode) {
        print('📊 App Initialization (With Session):');
        print('   Should Navigate to Home: ${result.shouldNavigateToHome}');
        print('   User Type: ${result.userType}');
        print('   Is Job Seeker: ${result.isJobSeeker}');
        print('   Has Error: ${result.hasError}');
      }

      assert(result.shouldNavigateToHome == true);
      assert(result.isJobSeeker == true);

      if (kDebugMode) {
        print('✅ App initialization test passed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ App initialization test failed: $e');
      }
    }
  }

  /// Print current session status
  Future<void> printSessionStatus() async {
    if (kDebugMode) {
      print('\n📊 Current Session Status:');

      final sessionManager = SessionManager.instance;
      final summary = await sessionManager.getSessionSummary();

      summary.forEach((key, value) {
        print('   $key: $value');
      });

      print('');
    }
  }
}
