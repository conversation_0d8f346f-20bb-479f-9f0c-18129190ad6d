/// API endpoints configuration for the Tareek application
class UserApiEndpoints {
  // Base URL
  static const String baseUrl = 'https://taraq-go.onrender.com';
  // API version
  static const String apiVersion = '/api/v1';
  // Full base URL with version
  static const String fullBaseUrl = '$baseUrl';

  // User Authentication endpoints
  static const String register = '/auth/api/register';
  static const String login = '/auth/api/login';
  static const String verifyUser = '/auth/api/verify';
  static const String resendOtp = '/auth/api/resendOtp';
  static const String forgotPassword = '/auth/api/forgotPassword';
  static const String resetPassword = '/auth/api/resetPassword';
  static const String googleSignIn = '/auth/api/google-login';
  static const String refreshToken = '/auth/api/refresh-token';

  // User Profile Management (all user types)
  static const String resetPasswordWithToken = '/user/api/resetPassword';
  static const String userProfile = '/user/api/profile';
  static const String updateUserProfile = '/user/api/profile';
  static const String uploadProfileImage = '/user/api/upload-image';
  static const String userLogout = '/user/api/logout';

  // User Event endpoints
  static const String sendEventOpinion = '/user/api/events/opinion';
  static const String addEventToFavorites = '/user/api/events/favorite';
  static const String getUserFavoriteEvents = '/user/api/events/favorite';
  static const String removeEventFromFavorites = '/user/api/events/favorite';
  static const String checkIfUserLikedEvent = '/user/api/events/check';
  static const String userToLikeEvent = '/user/api/events/like';
  static const String userToUnlikeEvent = '/user/api/events/unlike';
  static const String userToShareEvent = '/user/api/events/share';
  static const String checkIfUserFavoritedEvent = '/user/api/events/favorite/check';

  // User News endpoints
  static const String addNewsToFavorites = '/user/api/news/favorite';
  static const String removeNewsFromFavorites = '/user/api/news/favorite';
  static const String getUserFavoriteNews = '/user/api/news/favorite';
  static const String checkIfUserFavoritedNews = '/user/api/news/favorite/check';

  // User Notification endpoints
  static const String getUserNotifications = '/user/api/notifications';
  static const String markNotificationAsRead =
      '/user/api/notifications/mark-read'; // + /{notification_id}

  // Job Seeker specific endpoints
  static const String jobSeekerOnboarding = '/user/api/jobSeeker/onboarding';
  static const String getJobSeekerOnboarding = '/user/api/jobSeeker/onboarding';
  static const String updateJobSeekerOnboarding =
      '/user/api/jobSeeker/onboarding';
  static const String removeFavoriteJobPost =
      '/user/api/jobSeeker/job-posts/favorite';
  static const String addFavoriteJobPost =
      '/user/api/jobSeeker/job-posts/favorite';
  static const String getUserFavoriteJobPosts =
      '/user/api/jobSeeker/job-posts/favorite';
  static const String applyForJob = '/user/api/jobSeeker/job-applications';
  static const String getUserJobRecommendations =
      '/user/api/jobSeeker/job-recommendations'; // + /{user_id}

  // Recruiter specific endpoints
  static const String uploadJobPost = '/user/api/recruiter/job-post';
  static const String updateJobPost = '/user/api/recruiter/job-post'; // + /{id}
  static const String deleteJobPost = '/user/api/recruiter/job-post'; // + /{id}
  static const String getRecruiterStats = '/user/api/recruiter/stats';
  static const String getAllJobPostExceptCurrentRecruiter =
      '/user/api/recruiter/job-posts';
  static const String getApplicationDetails =
      '/user/api/recruiter/application'; // + /{job_post_id}/{user_id}
  static const String updateApplicationStage =
      '/user/api/recruiter/application-stage'; // + /{application_id}/{recruiter_id}

  // Common endpoints (no auth required)
  static const String allBlogs = '/user/api/common/blog';
  static const String blogById = '/user/api/common/blog'; // + /{id}
  static const String allNews = '/user/api/common/news';
  static const String newsById = '/user/api/common/news'; // + /{id}
  static const String newsCategoryById =
      '/user/common/api/news-category'; // + /{id}
  static const String newsCategory = '/user/api/common/news-categories';
  static const String blogCategoryById = '/user/api/common/category'; // + /{id}
  static const String blogCategory = '/user/api/common/category';
  static const String allEvents = '/user/api/common/events';
  static const String eventById = '/user/api/common/events'; // + /{id}
  static const String eventCategoryById =
      '/user/common/api/events-category'; // + /{id}
  static const String eventCategory = '/user/api/common/events-categories';
  static const String popularEvents = '/user/api/common/events/popular';
  static const String allJobPosts = '/user/api/common/job-post';
  static const String jobPostById = '/user/api/common/job-post'; // + /{id}
  static const String jobTypes = '/user/api/common/job-types';

  // Helper methods to build full URLs
  static String getFullUrl(String endpoint) {
    return '$fullBaseUrl$endpoint';
  }

  // Authentication URL helpers
  static String getRegisterUrl() {
    return '$baseUrl$register';
  }

  static String getLoginUrl() {
    return '$baseUrl$login';
  }

  static String getVerifyUserUrl() {
    return '$baseUrl$verifyUser';
  }

  static String getResendOtpUrl() {
    return '$baseUrl$resendOtp';
  }

  static String getForgotPasswordUrl() {
    return '$baseUrl$forgotPassword';
  }

  static String getResetPasswordUrl() {
    return '$fullBaseUrl$resetPassword';
  }

  // User Profile URL helpers
  static String getUserProfileUrl() {
    return '$fullBaseUrl$userProfile';
  }

  static String getResetPasswordWithTokenUrl() {
    return '$fullBaseUrl$resetPasswordWithToken';
  }

  static String getUpdateUserProfileUrl() {
    return '$fullBaseUrl$updateUserProfile';
  }

  static String getUploadProfileImageUrl() {
    return '$fullBaseUrl$uploadProfileImage';
  }

  static String getUserLogoutUrl() {
    return '$fullBaseUrl$userLogout';
  }

  // User Event URL helpers
  static String getSendEventOpinionUrl() {
    return '$fullBaseUrl$sendEventOpinion';
  }

  static String getAddEventToFavoritesUrl() {
    return '$fullBaseUrl$addEventToFavorites';
  }

  static String getUserFavoriteEventsUrl() {
    return '$fullBaseUrl$getUserFavoriteEvents';
  }

  static String getRemoveEventFromFavoritesUrl() {
    return '$fullBaseUrl$removeEventFromFavorites';
  }

  // User News URL helpers
  static String getAddNewsToFavoritesUrl() {
    return '$fullBaseUrl$addNewsToFavorites';
  }

  static String getRemoveNewsFromFavoritesUrl() {
    return '$fullBaseUrl$removeNewsFromFavorites';
  }

  static String getUserFavoriteNewsUrl() {
    return '$fullBaseUrl$getUserFavoriteNews';
  }

  // User Notification URL helpers
  static String getUserNotificationsUrl() {
    return '$fullBaseUrl$getUserNotifications';
  }

  static String getMarkNotificationAsReadUrl(String notificationId) {
    return '$fullBaseUrl$markNotificationAsRead/$notificationId';
  }

  // Job Seeker URL helpers
  static String getJobSeekerOnboardingUrl() {
    return '$fullBaseUrl$jobSeekerOnboarding';
  }

  static String getGetJobSeekerOnboardingUrl() {
    return '$fullBaseUrl$getJobSeekerOnboarding';
  }

  static String getUpdateJobSeekerOnboardingUrl() {
    return '$fullBaseUrl$updateJobSeekerOnboarding';
  }

  static String getRemoveFavoriteJobPostUrl() {
    return '$fullBaseUrl$removeFavoriteJobPost';
  }

  static String getAddFavoriteJobPostUrl() {
    return '$fullBaseUrl$addFavoriteJobPost';
  }

  static String getUserFavoriteJobPostsUrl() {
    return '$fullBaseUrl$getUserFavoriteJobPosts';
  }

  static String getApplyForJobUrl() {
    return '$fullBaseUrl$applyForJob';
  }

  static String getUserJobRecommendationsUrl(String userId) {
    return '$fullBaseUrl$getUserJobRecommendations/$userId';
  }

  // Recruiter URL helpers
  static String getUploadJobPostUrl() {
    return '$fullBaseUrl$uploadJobPost';
  }

  static String getUpdateJobPostUrl(String jobId) {
    return '$fullBaseUrl$updateJobPost/$jobId';
  }

  static String getDeleteJobPostUrl(String jobId) {
    return '$fullBaseUrl$deleteJobPost/$jobId';
  }

  static String getRecruiterStatsUrl() {
    return '$fullBaseUrl$getRecruiterStats';
  }

  static String getAllJobPostExceptCurrentRecruiterUrl() {
    return '$fullBaseUrl$getAllJobPostExceptCurrentRecruiter';
  }

  static String getApplicationDetailsUrl(String jobPostId, String userId) {
    return '$fullBaseUrl$getApplicationDetails/$jobPostId/$userId';
  }

  static String getUpdateApplicationStageUrl(
      String applicationId, String recruiterId) {
    return '$fullBaseUrl$updateApplicationStage/$applicationId/$recruiterId';
  }

  // Common endpoints URL helpers
  static String getAllBlogsUrl() {
    return '$fullBaseUrl$allBlogs';
  }

  static String getBlogByIdUrl(String blogId) {
    return '$fullBaseUrl$blogById/$blogId';
  }

  static String getAllEventsUrl() {
    return '$fullBaseUrl$allEvents';
  }

  static String getEventByIdUrl(String eventId) {
    return '$fullBaseUrl$eventById/$eventId';
  }

  static String getAllJobPostsUrl() {
    return '$fullBaseUrl$allJobPosts';
  }

  static String getJobPostByIdUrl(String jobId) {
    return '$fullBaseUrl$jobPostById/$jobId';
  }
}
