import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tareek/presentation/user_homepage_screen/discover_screen.dart';
import 'package:tareek/presentation/user_profile_screen/profile_screen.dart';

import '../presentation/user_homepage_screen/notification_screen.dart';
import '../presentation/user_homepage_screen/user_homepage.dart';

class BottomNavBar extends StatefulWidget {
  const BottomNavBar({super.key});

  @override
  _BottomNavBarState createState() => _BottomNavBarState();
}

class _BottomNavBarState extends State<BottomNavBar> {
  int _selectedIndex = 0;
  final PageController _pageController = PageController();

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
    _pageController.jumpToPage(index);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: <PERSON>View(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          HomeScreen(),
          DiscoverScreen(),
          NotificationsScreen(),
          ProfileScreen(),
        ],
      ),
      bottomNavigationBar: Theme(
        data: Theme.of(context).copyWith(
          canvasColor: Colors.white,
        ),
        child: BottomNavigationBar(
          currentIndex: _selectedIndex,
          onTap: _onItemTapped,
          selectedItemColor: Color(0xFF1F41BB),
          unselectedItemColor: Colors.grey,
          selectedLabelStyle: TextStyle(color: Color(0xFF1F41BB)),
          unselectedLabelStyle: TextStyle(color: Colors.grey),
          type: BottomNavigationBarType.fixed,
          items: [
            BottomNavigationBarItem(
              icon: SvgPicture.asset(
                'assets/house.svg',
                width: 24,
                height: 24,
                color: _selectedIndex == 0 ? Color(0xFF1F41BB) : Colors.grey,
              ),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: SvgPicture.asset(
                'assets/jobs.svg',
                width: 24,
                height: 24,
                color: _selectedIndex == 1 ? Color(0xFF1F41BB) : Colors.grey,
              ),
              label: 'Jobs',
            ),
            BottomNavigationBarItem(
              icon: SvgPicture.asset(
                'assets/notification.svg',
                width: 24,
                height: 24,
                color: _selectedIndex == 2 ? Color(0xFF1F41BB) : Colors.grey,
              ),
              label: 'Notifications',
            ),
            BottomNavigationBarItem(
              icon: SvgPicture.asset(
                'assets/settings.svg',
                width: 24,
                height: 24,
                color: _selectedIndex == 3 ? Color(0xFF1F41BB) : Colors.grey,
              ),
              label: 'My Profile',
            ),
          ],
        ),
      ),
    );
  }
}
