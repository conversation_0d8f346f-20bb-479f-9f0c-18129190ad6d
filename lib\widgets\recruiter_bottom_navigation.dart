import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tareek/presentation/recruiter_profile_screen/recruiter_profile.dart';
import 'package:tareek/presentation/user_homepage_screen/discover_screen.dart';
import '../presentation/recruiter_homepage_screen/create_job_screen.dart';
import '../presentation/user_homepage_screen/notification_screen.dart';
import '../presentation/user_homepage_screen/user_homepage.dart';

class RecruiterBottomNavBar extends StatefulWidget {
  const RecruiterBottomNavBar({super.key});

  @override
  _RecruiterBottomNavBarState createState() => _RecruiterBottomNavBarState();
}

class _RecruiterBottomNavBarState extends State<RecruiterBottomNavBar> {
  int _selectedIndex = 0;
  final PageController _pageController = PageController();

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
    _pageController.jumpToPage(index);
  }

  void _onFabPressed() {
    // Add your FAB functionality here
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateVacanciesScreen(),
      ),
    );
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Floating Action Button pressed!'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          HomeScreen(),
          DiscoverScreen(),
          NotificationsScreen(),
          ProfileHomeScreen(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _onFabPressed,
        backgroundColor: const Color(0xFF1F41BB),
        foregroundColor: Colors.white,
        elevation: 6,
        child: const Icon(
          Icons.add,
          size: 28,
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: Theme(
        data: Theme.of(context).copyWith(
          canvasColor: Colors.white,
        ),
        child: BottomAppBar(
          shape: const CircularNotchedRectangle(),
          notchMargin: 8,
          child: SizedBox(
            height: 60,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNavItem(0, 'assets/house.svg', 'Home'),
                _buildNavItem(1, 'assets/jobs.svg', 'Jobs'),
                const SizedBox(width: 40), // Space for FAB
                _buildNavItem(2, 'assets/notification.svg', 'Notifications'),
                _buildNavItem(3, 'assets/settings.svg', 'My Profile'),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, String iconPath, String label) {
    final isSelected = _selectedIndex == index;
    return GestureDetector(
      onTap: () => _onItemTapped(index),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            iconPath,
            width: 24,
            height: 24,
            color: isSelected ? const Color(0xFF1F41BB) : Colors.grey,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: isSelected ? const Color(0xFF1F41BB) : Colors.grey,
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}
